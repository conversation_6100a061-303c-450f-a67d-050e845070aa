package com.oaapproval.system.data.model

import com.google.gson.annotations.SerializedName

// 阅读优先级
enum class ReadingPriority(val displayName: String, val colorScheme: String) {
    NORMAL("普通", "green"),
    IMPORTANT("重要", "yellow"),
    URGENT("紧急", "red")
}

// 阅读状态
enum class ReadingStatus {
    PENDING,   // 待阅读
    COMPLETED  // 已完成
}

// 阅读步骤状态
enum class ReadingStepStatus {
    UNREAD,    // 未读
    READ       // 已读
}

// 阅读任务主体
data class Reading(
    val id: String,
    val title: String,
    val description: String?,
    val priority: ReadingPriority,
    val creator: User,
    val status: ReadingStatus,
    @SerializedName("submitTime")
    val submitTime: String,
    val attachments: List<ReadingAttachment> = emptyList(),
    val steps: List<ReadingStep> = emptyList(),
    val comments: List<ReadingComment> = emptyList()
)

// 阅读步骤
data class ReadingStep(
    val id: String,
    @SerializedName("readingId")
    val readingId: String,
    @SerializedName("readerId")
    val readerId: String,
    val reader: User,
    val status: ReadingStepStatus,
    @SerializedName("readTime")
    val readTime: String?,
    val comment: String?
)

// 阅读附件
data class ReadingAttachment(
    val id: String,
    @SerializedName("readingId")
    val readingId: String,
    @SerializedName("fileName")
    val fileName: String,
    @SerializedName("filePath")
    val filePath: String,
    @SerializedName("fileSize")
    val fileSize: Long?,
    @SerializedName("fileType")
    val fileType: String?,
    @SerializedName("uploadTime")
    val uploadTime: String
)

// 阅读意见
data class ReadingComment(
    val id: String,
    @SerializedName("readingId")
    val readingId: String,
    @SerializedName("commenterId")
    val commenterId: String,
    val commenter: User,
    val content: String,
    @SerializedName("commentTime")
    val commentTime: String
)

// 创建阅读请求
data class CreateReadingRequest(
    val title: String,
    val description: String?,
    val priority: String,
    @SerializedName("readerIds")
    val readerIds: List<String>,
    val attachments: List<ReadingAttachment>? = null
)

// 创建阅读响应
data class CreateReadingResponse(
    val message: String,
    @SerializedName("readingId")
    val readingId: String,
    val reading: Reading
)

// 确认已读请求
data class ConfirmReadingRequest(
    val comment: String? = null
)

// 添加阅读意见请求
data class AddReadingCommentRequest(
    val content: String
)
