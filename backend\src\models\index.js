const dbConfig = require("../config/db.config.js");
const Sequelize = require("sequelize");
const sequelize = new Sequelize(
  dbConfig.database, 
  dbConfig.username, 
  dbConfig.password, 
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    port: dbConfig.port,
    pool: {
      max: dbConfig.pool.max,
      min: dbConfig.pool.min,
      acquire: dbConfig.pool.acquire,
      idle: dbConfig.pool.idle
    },
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      timezone: '+08:00'
    },
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_general_ci',
      timestamps: true,
      underscored: true,
      freezeTableName: true
    },
    logging: dbConfig.logging || false
  }
);

const db = {};

db.Sequelize = Sequelize;
db.sequelize = sequelize;

// 导入模型
db.user = require("./user.model.js")(sequelize, Sequelize);
db.approval = require("./approval.model.js")(sequelize, Sequelize);
db.approvalStep = require("./approvalStep.model.js")(sequelize, Sequelize);
db.approvalComment = require("./approvalComment.model.js")(sequelize, Sequelize);
db.attachment = require("./attachment.model.js")(sequelize, Sequelize);
db.transferRecord = require("./transferRecord.model.js")(sequelize, Sequelize);

// 阅读功能模型
db.reading = require("./reading.model.js")(sequelize, Sequelize);
db.readingStep = require("./readingStep.model.js")(sequelize, Sequelize);
db.readingAttachment = require("./readingAttachment.model.js")(sequelize, Sequelize);
db.readingComment = require("./readingComment.model.js")(sequelize, Sequelize);

// 设置关联关系
// 用户和审批申请的关系（一对多）
db.user.hasMany(db.approval, { as: "approvals", foreignKey: "applicantId", constraints: false });
db.approval.belongsTo(db.user, { as: "applicant", foreignKey: "applicantId", constraints: false });

// 审批申请和审批步骤的关系（一对多）
db.approval.hasMany(db.approvalStep, {
  as: "steps",
  foreignKey: "approvalId",
  constraints: false
});
db.approvalStep.belongsTo(db.approval, {
  foreignKey: "approvalId",
  constraints: false
});

// 用户和审批步骤的关系（一对多）
db.user.hasMany(db.approvalStep, {
  as: "approvalSteps",
  foreignKey: "approverId",
  constraints: false
});
db.approvalStep.belongsTo(db.user, {
  as: "approver",
  foreignKey: "approverId",
  constraints: false
});

// 审批申请和审批意见的关系（一对多）
db.approval.hasMany(db.approvalComment, {
  as: "comments",
  foreignKey: "approvalId",
  constraints: false
});
db.approvalComment.belongsTo(db.approval, {
  foreignKey: "approvalId",
  constraints: false
});

// 用户和审批意见的关系（一对多）
db.user.hasMany(db.approvalComment, {
  as: "comments",
  foreignKey: "commenterId",
  constraints: false
});
db.approvalComment.belongsTo(db.user, {
  as: "commenter",
  foreignKey: "commenterId",
  constraints: false
});

// 审批申请和附件的关系（一对多）
db.approval.hasMany(db.attachment, {
  as: "attachments",
  foreignKey: "approvalId",
  constraints: false  // 禁用数据库级外键约束
});
db.attachment.belongsTo(db.approval, {
  foreignKey: "approvalId",
  constraints: false  // 禁用数据库级外键约束
});

// 审批申请和流转记录的关系（一对多）
db.approval.hasMany(db.transferRecord, {
  as: "transferRecords",
  foreignKey: "approvalId",
  constraints: false
});
db.transferRecord.belongsTo(db.approval, {
  foreignKey: "approvalId",
  constraints: false
});

// 用户和流转记录的关系（一对多）- 发起人
db.user.hasMany(db.transferRecord, {
  as: "sentTransfers",
  foreignKey: "fromUserId",
  constraints: false
});
db.transferRecord.belongsTo(db.user, {
  as: "fromUser",
  foreignKey: "fromUserId",
  constraints: false
});

// 用户和流转记录的关系（一对多）- 接收人
db.user.hasMany(db.transferRecord, {
  as: "receivedTransfers",
  foreignKey: "toUserId",
  constraints: false
});
db.transferRecord.belongsTo(db.user, {
  as: "toUser",
  foreignKey: "toUserId",
  constraints: false
});

// 阅读功能关联关系
// 用户和阅读任务的关系（一对多）
db.user.hasMany(db.reading, {
  as: "readings",
  foreignKey: "creatorId",
  constraints: false
});
db.reading.belongsTo(db.user, {
  as: "creator",
  foreignKey: "creatorId",
  constraints: false
});

// 阅读任务和阅读步骤的关系（一对多）
db.reading.hasMany(db.readingStep, {
  as: "steps",
  foreignKey: "readingId",
  constraints: false
});
db.readingStep.belongsTo(db.reading, {
  foreignKey: "readingId",
  constraints: false
});

// 用户和阅读步骤的关系（一对多）
db.user.hasMany(db.readingStep, {
  as: "readingSteps",
  foreignKey: "readerId",
  constraints: false
});
db.readingStep.belongsTo(db.user, {
  as: "reader",
  foreignKey: "readerId",
  constraints: false
});

// 阅读任务和阅读附件的关系（一对多）
db.reading.hasMany(db.readingAttachment, {
  as: "attachments",
  foreignKey: "readingId",
  constraints: false
});
db.readingAttachment.belongsTo(db.reading, {
  foreignKey: "readingId",
  constraints: false
});

// 阅读任务和阅读意见的关系（一对多）
db.reading.hasMany(db.readingComment, {
  as: "comments",
  foreignKey: "readingId",
  constraints: false
});
db.readingComment.belongsTo(db.reading, {
  foreignKey: "readingId",
  constraints: false
});

// 用户和阅读意见的关系（一对多）
db.user.hasMany(db.readingComment, {
  as: "readingComments",
  foreignKey: "commenterId",
  constraints: false
});
db.readingComment.belongsTo(db.user, {
  as: "commenter",
  foreignKey: "commenterId",
  constraints: false
});

module.exports = db;