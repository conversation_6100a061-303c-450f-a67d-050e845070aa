package com.oaapproval.system.ui.screens.approval

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.oaapproval.system.data.model.User
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.viewmodel.AuthViewModel
import com.oaapproval.system.viewmodel.UserViewModel
import com.oaapproval.system.ui.components.MainScaffold
import kotlinx.coroutines.launch
import com.oaapproval.system.ui.components.StatusBar
import com.oaapproval.system.ui.utils.SystemBarsUtils

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectApproversScreen(
    navController: NavController,
    authViewModel: AuthViewModel = viewModel(),
    userViewModel: UserViewModel = viewModel(),
    mode: String = "multi", // "multi" for 多选审批人, "single" for 单选流转对象, "reading" for 阅读对象
    title: String = "选择对象",
    excludeUserId: String = "", // 需要排除的用户ID（如申请人ID）
    onUserSelected: (String) -> Unit = {} // 单选模式的回调
) {
    // 获取外部文件URI
    val externalFileUri = com.oaapproval.system.MainActivity.externalFileUri?.toString()

    
    // 收集ViewModel中的状态
    val currentUser by authViewModel.currentUser.collectAsState()
    val allUsers by authViewModel.allUsers.collectAsState()
    val isLoading = authViewModel.isLoading
    
    // 筛选出除当前用户和排除用户外的所有用户
    val userList = remember(currentUser, allUsers, excludeUserId) {
        allUsers.filter { user ->
            user.id != currentUser?.id && // 排除当前用户
            (excludeUserId.isBlank() || user.id != excludeUserId) // 排除指定用户（如申请人）
        }
    }
    
    // 初始化时加载数据
    LaunchedEffect(Unit) {
        authViewModel.getCurrentUser()
        authViewModel.getAllUsers()
    }
    
    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }
    val filteredUsers = remember(searchQuery, userList) {
        if (searchQuery.isBlank()) {
            userList
        } else {
            userList.filter {
                it.realName.contains(searchQuery, ignoreCase = true) ||
                (it.position?.contains(searchQuery, ignoreCase = true) == true)
            }
        }
    }
    
    // 已选择的审批人列表
    var selectedApprovers by remember { mutableStateOf<List<User>>(emptyList()) }
    val maxApprovers = 3
    
    // 协程作用域
    val coroutineScope = rememberCoroutineScope()
    
    MainScaffold(
        navController = navController,
        title = title,
        actions = {
            TextButton(
                onClick = {
                    when (mode) {
                        "reading" -> {
                            // 阅读模式：将选择的用户ID保存到UserViewModel，然后导航到创建阅读页面
                            val readerIds = selectedApprovers.map { it.id }
                            userViewModel.setSelectedUserIds(readerIds)
                            navController.navigate(Screen.CreateReading.route) {
                                popUpTo(Screen.SelectApprovers.route) { inclusive = true }
                            }
                        }
                        "single" -> {
                            // 单选模式：调用回调函数
                            if (selectedApprovers.isNotEmpty()) {
                                onUserSelected(selectedApprovers.first().id)
                                navController.popBackStack()
                            }
                        }
                        else -> {
                            // 多选审批人模式：原有逻辑
                            val approverIds = selectedApprovers.map { it.id }
                            val approverIdsParam = approverIds.joinToString(",")
                            val navigationUrl = if (externalFileUri != null) {
                                "${Screen.CreateApproval.route}?approverId=$approverIdsParam&externalFileUri=$externalFileUri"
                            } else {
                                "${Screen.CreateApproval.route}?approverId=$approverIdsParam"
                            }
                            navController.navigate(navigationUrl) {
                                popUpTo(Screen.SelectApprovers.route) { inclusive = true }
                            }
                        }
                    }
                },
                enabled = selectedApprovers.isNotEmpty()
            ) {
                Text(
                    "确定",
                    color = if (selectedApprovers.isNotEmpty()) Color(0xFF3B82F6) else Color(0xFF9CA3AF),
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                )
            }
        }
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            
            // 搜索框
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 4.dp, vertical = 4.dp),
                    placeholder = { Text("搜索姓名、部门或职位", color = Color.Gray, fontSize = 14.sp) },
                    leadingIcon = {
                        Icon(Icons.Default.Search, contentDescription = "搜索", tint = Color.Gray)
                    },
                    singleLine = true,
                    shape = RoundedCornerShape(8.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedContainerColor = Color.White,
                        unfocusedContainerColor = Color.White,
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent
                    ),
                    textStyle = androidx.compose.ui.text.TextStyle(
                        fontSize = 14.sp,
                        color = Color(0xFF333333)
                    )
                )
            }
            

            
            // 可选择的审批人区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.People,
                    contentDescription = null,
                    tint = Color(0xFF64748B),
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "可选择的审批人",
                    color = Color(0xFF64748B),
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
            }
            
            // 主要内容区域
            Box(
                modifier = Modifier.weight(1f)
            ) {
                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = Color(0xFF3B82F6))
                    }
                } else if (filteredUsers.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Default.SearchOff,
                                contentDescription = null,
                                modifier = Modifier.size(64.dp),
                                tint = Color(0xFFCBD5E1)
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "未找到匹配的用户",
                                color = Color(0xFF94A3B8)
                            )
                        }
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(filteredUsers) { user ->
                            ApproverItem(
                                user = user,
                                isSelected = selectedApprovers.contains(user),
                                onSelect = {
                                    if (mode == "single") {
                                        // 单选模式：先立即返回页面，再异步处理选择逻辑
                                        navController.popBackStack()
                                        // 使用 post 确保页面跳转完成后再执行回调
                                        android.os.Handler(android.os.Looper.getMainLooper()).post {
                                            onUserSelected(user.id)
                                        }
                                    } else {
                                        // 多选模式：原有逻辑
                                        selectedApprovers = if (selectedApprovers.contains(user)) {
                                            // 如果已选择，则移除
                                            selectedApprovers.filter { it.id != user.id }
                                        } else {
                                            // 如果未选择且未达到上限，则添加
                                            if (selectedApprovers.size < maxApprovers) {
                                                selectedApprovers + user
                                            } else {
                                                selectedApprovers
                                            }
                                        }
                                    }
                                }
                            )
                        }
                        
                        item {
                            // 为底部确认按钮预留空间，避免列表内容被遮挡
                            val bottomSpacing = if (selectedApprovers.isNotEmpty()) {
                                // 有确认按钮时，预留按钮高度 + 安全间距
                                val safeBottomPadding = SystemBarsUtils.getSafeBottomPadding(extraPadding = 16.dp)
                                48.dp + 32.dp + safeBottomPadding // 按钮高度 + 上下padding + 安全间距
                            } else {
                                // 没有确认按钮时，只添加基本间距
                                SystemBarsUtils.getSafeBottomPadding(extraPadding = 8.dp)
                            }
                            Spacer(modifier = Modifier.height(bottomSpacing))
                        }
                    }
                }
            }
            
            // 底部确认按钮 - 确保用户可以看到确认按钮
            if (selectedApprovers.isNotEmpty()) {
                // 获取安全的底部间距，避免被系统导航栏挡住
                val safeBottomPadding = SystemBarsUtils.getSafeBottomPadding(extraPadding = 16.dp)

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = 16.dp,
                            end = 16.dp,
                            top = 16.dp,
                            bottom = safeBottomPadding
                        )
                ) {
                    Button(
                        onClick = {
                            // 确认选择，返回所选审批人ID列表
                            val approverIds = selectedApprovers.map { it.id }
                            // 构建参数字符串，格式为 id1,id2,id3
                            val approverIdsParam = approverIds.joinToString(",")
                            // 构建导航URL，包含外部文件URI（如果有的话）
                            val navigationUrl = if (externalFileUri != null) {
                                "${Screen.CreateApproval.route}?approverId=$approverIdsParam&externalFileUri=$externalFileUri"
                            } else {
                                "${Screen.CreateApproval.route}?approverId=$approverIdsParam"
                            }
                            navController.navigate(navigationUrl) {
                                popUpTo(Screen.SelectApprovers.route) { inclusive = true }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF3B82F6)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            "确认选择 (${selectedApprovers.size}人)",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}



@Composable
fun ApproverItem(
    user: User,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onSelect),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 用户头像 - 使用蓝色圆形背景
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFF3B82F6)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = user.realName.firstOrNull()?.toString() ?: "?",
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // 用户信息
                Column {
                    Text(
                        text = user.realName,
                        fontWeight = FontWeight.Medium,
                        fontSize = 15.sp,
                        color = Color(0xFF1F2937)
                    )
                    
                    Text(
                        text = user.position ?: "员工",
                        fontSize = 13.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
            
            // 圆形选择按钮
            Box(
                modifier = Modifier
                    .size(22.dp)
                    .clip(CircleShape)
                    .background(
                        if (isSelected) Color(0xFF3B82F6)
                        else Color.White
                    )
                    .border(
                        width = 1.dp,
                        color = if (isSelected) Color(0xFF3B82F6) else Color(0xFFCBD5E1),
                        shape = CircleShape
                    )
                    .clickable { onSelect() },
                contentAlignment = Alignment.Center
            ) {
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }
} 