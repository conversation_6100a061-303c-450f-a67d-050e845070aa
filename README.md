# OA审批系统 - 完整项目文档

## 📋 项目概述

OA审批系统是一个基于Android客户端和Node.js后端的企业级审批流程管理系统。系统采用前后端分离架构，支持多级审批、流程流转、附件管理等功能。

### 🎯 核心功能
- **用户认证**：登录/注册、JWT令牌管理
- **审批管理**：创建申请、多级审批、流程流转
- **🆕 阅读管理**：发起阅读、状态跟踪、反馈收集
- **用户管理**：员工信息、领导权限、组织架构
- **附件管理**：文件上传、下载、预览
- **通知系统**：审批状态变更通知
- **🆕 微信文件直接审批**：支持从微信使用其他应用打开文件直接发起审批流程

## 🆕 新功能需求

### 发起阅读功能 (计划开发)

#### 📚 **功能描述**
实现独立的文档阅读分享功能，与发起审批功能流程类似但完全独立，用户可以发起阅读任务，指定阅读对象，分享文档、通知、公告等内容。阅读详情界面参照审批详情设计，但使用独立的数据结构和界面。

#### 🔄 **用户操作流程**
1. **发起阅读**: 用户点击"发起阅读"按钮
2. **选择阅读对象**: 选择需要阅读的人员（新建选择界面）
3. **填写阅读内容**:
   - 阅读标题（必填）
   - 阅读内容/描述（可选）
   - 附件上传（可选）
4. **提交发起**: 系统发送阅读通知给指定人员
5. **查看详情**: 阅读详情界面参照审批详情设计

#### 👥 **阅读对象操作流程**
1. **接收通知**: 收到阅读任务通知
2. **查看内容**: 点击查看详细阅读内容
3. **确认已读**: 阅读完成后点击"确认已读"按钮
4. **添加意见**: 可选择添加阅读意见

#### 🏷️ **阅读状态管理**
- **未读**: 刚发起，阅读对象尚未查看
- **已读**: 阅读对象已确认阅读

#### 🔧 **技术实现要点**

##### 数据模型设计（全新独立）
```kotlin
// 全新的阅读数据模型
data class Reading(
    val id: String,
    val title: String,
    val description: String,
    val priority: ReadingPriority,
    val creatorId: String,
    val creator: User,
    val status: ReadingStatus,
    val submitTime: String,
    val attachments: List<ReadingAttachment> = emptyList(),
    val readers: List<ReadingStep> = emptyList(),
    val comments: List<ReadingComment> = emptyList()
)

// 阅读步骤
data class ReadingStep(
    val id: String,
    val readingId: String,
    val readerId: String,
    val reader: User,
    val status: ReadingStepStatus,
    val readTime: String? = null,
    val comment: String = ""
)

// 阅读优先级
enum class ReadingPriority {
    NORMAL,    // 普通
    IMPORTANT, // 重要
    URGENT     // 紧急
}

// 阅读状态
enum class ReadingStatus {
    PENDING,   // 待阅读
    COMPLETED  // 已完成
}

// 阅读步骤状态
enum class ReadingStepStatus {
    UNREAD,    // 未读
    READ       // 已读
}

// 阅读附件
data class ReadingAttachment(
    val id: String,
    val readingId: String,
    val fileName: String,
    val filePath: String,
    val fileSize: Long,
    val fileType: String,
    val uploadTime: String
)

// 阅读意见
data class ReadingComment(
    val id: String,
    val readingId: String,
    val commenterId: String,
    val commenter: User,
    val content: String,
    val commentTime: String
)
```

##### API接口设计（全新独立）
```javascript
// 后端API路由（全新独立）
POST /api/reading/                // 创建阅读任务
GET  /api/reading/                // 获取阅读列表
GET  /api/reading/my              // 我发起的阅读
GET  /api/reading/pending         // 待我阅读的
GET  /api/reading/:id             // 阅读详情
POST /api/reading/:id/confirm     // 确认已读
POST /api/reading/:id/comment     // 添加阅读意见
GET  /api/reading/:id/attachments // 获取阅读附件
POST /api/reading/:id/attachments // 上传阅读附件
```

##### 数据库设计（全新独立表结构）
```sql
-- 阅读任务表
CREATE TABLE readings (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    priority VARCHAR(20) DEFAULT 'NORMAL',
    creator_id VARCHAR(36) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    submit_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id)
);

-- 阅读步骤表
CREATE TABLE reading_steps (
    id VARCHAR(36) PRIMARY KEY,
    reading_id VARCHAR(36) NOT NULL,
    reader_id VARCHAR(36) NOT NULL,
    status VARCHAR(20) DEFAULT 'UNREAD',
    read_time DATETIME,
    comment TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reading_id) REFERENCES readings(id),
    FOREIGN KEY (reader_id) REFERENCES users(id)
);

-- 阅读附件表
CREATE TABLE reading_attachments (
    id VARCHAR(36) PRIMARY KEY,
    reading_id VARCHAR(36) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reading_id) REFERENCES readings(id)
);

-- 阅读意见表
CREATE TABLE reading_comments (
    id VARCHAR(36) PRIMARY KEY,
    reading_id VARCHAR(36) NOT NULL,
    commenter_id VARCHAR(36) NOT NULL,
    content TEXT NOT NULL,
    comment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reading_id) REFERENCES readings(id),
    FOREIGN KEY (commenter_id) REFERENCES users(id)
);
```

#### 📱 **Android实现细节**

##### 全新界面组件
- **发起阅读界面**: 新建`CreateReadingScreen`
- **阅读列表界面**: 新建`ReadingListScreen`
- **阅读详情界面**: 新建`ReadingDetailScreen`
- **选择阅读对象**: 新建`SelectReadersScreen`

##### 代码实现
```kotlin
// 全新的ReadingViewModel
class ReadingViewModel : ViewModel() {
    private val readingRepository = ReadingRepository()

    var isLoading by mutableStateOf(false)
    var errorMessage by mutableStateOf<String?>(null)

    fun createReading(
        title: String,
        description: String,
        priority: String,
        readers: List<String>,
        attachments: List<ReadingAttachment>?
    ) {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null

            try {
                val result = readingRepository.createReading(
                    title = title,
                    description = description,
                    priority = priority,
                    readerIds = readers,
                    attachments = attachments
                )

                if (result.isSuccess) {
                    // 创建成功处理
                } else {
                    errorMessage = result.exceptionOrNull()?.message
                }
            } catch (e: Exception) {
                errorMessage = e.message
            } finally {
                isLoading = false
            }
        }
    }

    fun confirmReading(readingId: String, comment: String = "") {
        viewModelScope.launch {
            try {
                readingRepository.confirmReading(readingId, comment)
                // 更新本地状态
            } catch (e: Exception) {
                errorMessage = e.message
            }
        }
    }
}

// 全新的ReadingRepository
class ReadingRepository {
    private val apiService = ApiClient.readingService

    suspend fun createReading(
        title: String,
        description: String,
        priority: String,
        readerIds: List<String>,
        attachments: List<ReadingAttachment>?
    ): Result<Reading> {
        return try {
            val response = apiService.createReading(
                CreateReadingRequest(
                    title = title,
                    description = description,
                    priority = priority,
                    readerIds = readerIds,
                    attachments = attachments
                )
            )
            if (response.isSuccessful) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("创建阅读失败"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

#### 🎯 **应用场景**
- **公司通知**: 政策发布、制度更新
- **培训材料**: 学习文档、操作手册
- **会议纪要**: 会议记录、决议事项
- **技术文档**: 技术规范、开发指南

### 微信文件直接发起审批功能 (计划开发)

#### 📱 **功能描述**
实现从微信选择"使用其他应用打开"文件（PDF、WPS文档、图片），直接在OA审批系统发起审批并跳转到选择对象页面，接着到创建申请界面，默认显示从微信传入的附件。

#### 🎯 **用户操作流程**
1. **微信文件选择**: 用户在微信中选择PDF、WPS文档或图片文件
2. **使用其他应用打开**: 选择"使用其他应用打开" → 选择OA审批系统
3. **自动发起审批**: 系统自动启动审批流程创建
4. **选择审批对象**: 直接跳转到"选择审批对象"页面
5. **创建申请界面**: 进入"创建申请"界面
6. **附件预填充**: 界面中默认显示从微信传入的文件附件

#### 📋 **支持的文件类型**
- **PDF文档**: `.pdf`
- **WPS文档**: `.wps`, `.doc`, `.docx`
- **图片文件**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`

#### � **支持的应用场景**
- **微信聊天文件**: 聊天中的文档和图片
- **微信收藏文件**: 收藏夹中的文档和图片
- **微信朋友圈图片**: 保存的朋友圈图片
- **微信文件传输助手**: 传输的文档文件

#### �🔧 **技术实现要点**
- **Intent Filter配置**: 配置应用为PDF、WPS、图片文件的可选打开方式
- **ACTION_VIEW支持**: 支持微信"使用其他应用打开"功能
- **文件接收处理**: 接收微信传入的文件数据和URI
- **自动流程触发**: 跳过常规首页，直接进入审批创建流程
- **文件预处理**: 自动将接收的文件添加到申请的附件列表
- **流程保持**: 保持正常的审批流程（选择对象 → 创建申请 → 提交）
- **错误处理**: 处理文件格式不支持、文件过大等异常情况

#### 🎨 **用户体验优化**
- **无缝衔接**: 从微信到OA系统的无缝操作体验
- **快速创建**: 减少用户手动上传附件的步骤
- **智能识别**: 根据文件类型自动设置合适的审批模板
- **进度提示**: 显示文件处理和审批创建的进度状态

#### 📱 **Android实现细节**
```xml
<!-- AndroidManifest.xml Intent Filter配置示例 -->
<!-- 支持微信"使用其他应用打开"功能 (ACTION_VIEW) -->
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <!-- PDF文档 -->
    <data android:mimeType="application/pdf" />
    <!-- WPS文档 -->
    <data android:mimeType="application/msword" />
    <data android:mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
    <data android:mimeType="application/vnd.ms-works" />
    <!-- 图片文件 -->
    <data android:mimeType="image/*" />
</intent-filter>
```

#### 🔄 **开发优先级**
- **P0 (高优先级)**: PDF文件支持
- **P1 (中优先级)**: WPS文档支持
- **P2 (低优先级)**: 图片文件支持

---

## 🔧 重要修复记录

### 最新修复 (2025-01-18)

#### 🗑️ **删除审批类型功能**
- **原因**: 项目不分申请类型，统一处理所有审批
- **修改范围**:
  - ✅ 删除前端所有审批类型显示UI
  - ✅ 删除后端type字段相关逻辑
  - ✅ 删除数据库type字段定义
  - ✅ 删除ApprovalType枚举类
  - ✅ 清理所有type相关的查询参数
  - ✅ 删除测试编码显示（如"OTHER•APbd74e"）

#### 📱 **前端修改详情**
- `ApprovalDetailScreen.kt`: 删除审批类型和测试编码显示
- `ApprovalHistoryScreen.kt`: 删除类型显示
- `PendingApprovalsScreen.kt`: 删除类型显示和基于类型的优先级标签
- `MyApprovalsScreen.kt`: 删除类型显示
- `Approval.kt`: 删除ApprovalType枚举和type字段
- `ApiService.kt`: 删除type查询参数
- `ApprovalRepository.kt`: 删除type相关方法参数
- `ApprovalViewModel.kt`: 删除type参数

#### 🔧 **后端修改详情**
- `approval.controller.js`: 删除type字段处理和查询参数
- `approval.model.js`: 删除type字段定义
- `init_db.sql`: 删除type字段和ENUM定义

### 历史重要修复

#### 🔄 **流转功能全面修复**
- **问题**: 流转历史显示不清晰，权限控制不当
- **解决**:
  - 优化流转备注显示格式："张三 将申请流转给 李四，备注：请帮忙处理"
  - 修复自己审批自己申请的权限问题
  - 增强流转权限控制逻辑
  - 完善流转历史记录展示

#### 📝 **审批流程优化**
- **问题**: 审批步骤显示混乱，流程状态不同步
- **解决**:
  - 重构审批步骤构建逻辑
  - 修复流程状态实时同步
  - 优化审批意见展示格式
  - 增强流程可视化效果

#### 💾 **数据持久化增强**
- **问题**: 数据丢失，离线同步失效
- **解决**:
  - 实现本地数据缓存机制
  - 添加离线数据同步功能
  - 修复数据加载性能问题
  - 优化数据恢复逻辑

#### 📎 **附件功能完善**
- **问题**: 附件类型不匹配，上传失败
- **解决**:
  - 修复AttachmentData类型不匹配问题
  - 优化文件上传流程
  - 增强附件预览功能
  - 修复附件下载问题

#### 🐛 **编译问题解决**
- **问题**: 中文字符编译错误，依赖冲突
- **解决**:
  - 修复中文字符编译错误
  - 解决Gradle依赖冲突
  - 优化Kotlin编译配置
  - 修复编译警告和错误

#### 👤 **用户体验优化**
- **问题**: 主语显示不清晰，备注筛选不准确
- **解决**:
  - 优化审批意见主语显示
  - 增强备注筛选功能
  - 改进用户界面交互
  - 提升整体用户体验

## 🏗️ 系统架构

```
┌─────────────────┐    HTTP/HTTPS     ┌─────────────────┐
│   Android客户端  │ ◄──────────────► │   Node.js后端   │
│   (Kotlin/Jetpack) │                │   (Express.js)  │
└─────────────────┘                  └─────────┬───────┘
                                               │
                                               ▼
                                     ┌─────────────────┐
                                     │   SQLite数据库   │
                                     │   (本地文件)     │
                                     └─────────────────┘
```

### 🔄 数据流向
1. **Android客户端** → HTTP请求 → **后端API**
2. **后端API** → SQL查询 → **SQLite数据库**
3. **数据库** → 查询结果 → **后端API**
4. **后端API** → JSON响应 → **Android客户端**

## 📁 项目结构

```
OA/
├── app/                          # Android应用源码
│   ├── src/main/java/com/oaapproval/system/
│   │   ├── data/                 # 数据层
│   │   │   ├── api/             # API接口定义
│   │   │   ├── model/           # 数据模型
│   │   │   └── repository/      # 数据仓库
│   │   ├── ui/                  # UI层
│   │   │   ├── screens/         # 页面组件
│   │   │   ├── components/      # 可复用组件
│   │   │   ├── navigation/      # 导航管理
│   │   │   └── theme/           # 主题样式
│   │   ├── viewmodel/           # 视图模型
│   │   ├── MainActivity.kt      # 主Activity
│   │   └── OAApplication.kt     # 应用入口
│   ├── build.gradle.kts         # Android构建配置
│   └── README.md               # Android应用文档
├── backend/                     # Node.js后端源码
│   ├── src/
│   │   ├── config/             # 配置文件
│   │   │   └── db.config.js    # 数据库配置
│   │   ├── controllers/        # 控制器
│   │   │   ├── auth.controller.js
│   │   │   ├── user.controller.js
│   │   │   └── approval.controller.js
│   │   ├── middleware/         # 中间件
│   │   │   ├── auth.js         # JWT认证
│   │   │   └── verifySignUp.js # 注册验证
│   │   ├── models/             # 数据模型
│   │   │   ├── index.js        # 模型入口
│   │   │   ├── user.model.js   # 用户模型
│   │   │   ├── approval.model.js # 审批模型
│   │   │   └── ...             # 其他模型
│   │   ├── routes/             # 路由定义
│   │   │   ├── auth.routes.js  # 认证路由
│   │   │   ├── user.routes.js  # 用户路由
│   │   │   └── approval.routes.js # 审批路由
│   │   └── server.js           # 服务器入口
│   ├── public/                 # 静态文件
│   ├── uploads/                # 上传文件存储
│   ├── logs/                   # 日志文件
│   ├── database.sqlite         # SQLite数据库文件
│   ├── package.json           # Node.js依赖配置
│   ├── auto_deploy.sh         # 自动部署脚本
│   └── README.md              # 后端文档
├── design/                     # 设计文档
└── README.md                  # 项目总文档
```

## 🔗 组件交互关系

### Android应用架构 (MVVM)

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Screen    │ ◄─ │  ViewModel  │ ◄─ │ Repository  │
│   (UI层)    │    │  (业务逻辑)  │    │  (数据层)   │
└─────────────┘    └─────────────┘    └──────┬──────┘
                                              │
                                              ▼
                                    ┌─────────────┐
                                    │ ApiService  │
                                    │ (网络请求)   │
                                    └─────────────┘
```

#### 核心组件说明

**1. ApiClient.kt** - 网络请求客户端
- 管理Retrofit实例
- 处理JWT令牌
- 自动环境检测（模拟器/真机）
- 请求/响应拦截器

**2. Repository层** - 数据仓库
- `UserRepository`: 用户相关操作
- `ApprovalRepository`: 审批相关操作
- 统一错误处理和数据转换

**3. ViewModel层** - 业务逻辑
- `AuthViewModel`: 认证状态管理
- `ApprovalViewModel`: 审批流程管理
- 状态管理和UI更新

**4. Screen层** - 用户界面
- `LoginScreen`: 登录页面
- `EmployeeHomeScreen`: 员工主页
- `CreateApprovalScreen`: 创建审批
- `ApprovalDetailScreen`: 审批详情

### 后端API架构 (MVC)

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Routes    │ ─► │ Controllers │ ─► │   Models    │
│  (路由层)    │    │  (控制器)    │    │  (数据层)   │
└─────────────┘    └─────────────┘    └──────┬──────┘
                                              │
                                              ▼
                                    ┌─────────────┐
                                    │  Database   │
                                    │  (SQLite)   │
                                    └─────────────┘
```

#### 核心组件说明

**1. 路由层 (Routes)**
- `auth.routes.js`: 认证相关路由 (`/api/auth/*`)
- `user.routes.js`: 用户管理路由 (`/api/users/*`)
- `approval.routes.js`: 审批管理路由 (`/api/approvals/*`)

**2. 控制器层 (Controllers)**
- `auth.controller.js`: 处理登录、注册逻辑
- `user.controller.js`: 处理用户CRUD操作
- `approval.controller.js`: 处理审批流程逻辑

**3. 模型层 (Models)**
- `user.model.js`: 用户数据模型
- `approval.model.js`: 审批数据模型
- `approvalStep.model.js`: 审批步骤模型
- 使用Sequelize ORM管理数据关系

**4. 中间件 (Middleware)**
- `auth.js`: JWT令牌验证
- `verifySignUp.js`: 注册数据验证
- CORS跨域处理

## 🗄️ 数据库设计

### 核心表结构

**users (用户表)**
```sql
- id: VARCHAR(36) PRIMARY KEY
- username: VARCHAR(50) UNIQUE
- password: VARCHAR(255)
- real_name: VARCHAR(100)
- position: VARCHAR(100)
- phone: VARCHAR(20)
```

**approvals (审批表)**
```sql
- id: VARCHAR(36) PRIMARY KEY
- request_number: VARCHAR(50) UNIQUE
- title: VARCHAR(200)
- status: VARCHAR(50)
- priority: VARCHAR(50)
- applicant_id: VARCHAR(36) FK
- current_step: INTEGER
- submit_time: DATETIME
- description: TEXT
```

**approval_steps (审批步骤表)**
```sql
- id: VARCHAR(36) PRIMARY KEY
- approval_id: VARCHAR(36) FK
- step_name: VARCHAR(100)
- step_order: INTEGER
- approver_id: VARCHAR(36) FK
- status: VARCHAR(50)
- process_time: DATETIME
- comment: TEXT
```

### 表关系
- `users` 1:N `approvals` (一个用户可以创建多个审批)
- `approvals` 1:N `approval_steps` (一个审批有多个步骤)
- `users` 1:N `approval_steps` (一个用户可以审批多个步骤)

## 🌐 API接口文档

### 认证接口
```
POST /api/auth/signin     # 用户登录
POST /api/auth/signup     # 用户注册
```

### 用户接口
```
GET  /api/users          # 获取所有用户
GET  /api/users/me       # 获取当前用户信息
GET  /api/users/:id      # 获取指定用户信息
PUT  /api/users/:id      # 更新用户信息
```

### 审批接口
```
GET  /api/approvals           # 获取审批列表
POST /api/approvals           # 创建审批申请
GET  /api/approvals/:id       # 获取审批详情
PUT  /api/approvals/:id       # 更新审批状态
POST /api/approvals/:id/process # 处理审批
POST /api/approvals/:id/transfer # 流转审批
```

## 🚀 快速启动指南

### 本地开发环境
```bash
# 1. 启动后端服务
cd backend
npm install
node src/server.js
# 服务运行在 http://localhost:3000

# 2. 运行Android应用
# 在Android Studio中打开项目并运行
# 模拟器API地址: http://********:3000/api/
# 真机API地址: http://192.168.x.x:3000/api/
```

### 📱 APK生成指南

#### 生成方法
```bash
# 生成Debug版本（用于测试）
./gradlew assembleDebug

# 生成Release版本（用于发布）
./gradlew assembleRelease

# 清理并重新构建
./gradlew clean assembleRelease
```

#### APK文件位置
- **Debug版本**: `app/build/outputs/apk/debug/app-debug.apk` (~18MB)
- **Release版本**: `app/build/outputs/apk/release/app-release-unsigned.apk` (~13MB)

#### 安装方法
```bash
# 通过ADB安装
adb install app/build/outputs/apk/debug/app-debug.apk

# 或直接将APK文件传输到设备安装
```

## 🚀 部署指南

### 生产环境部署

#### 🔧 自动部署（推荐）
```bash
cd backend
chmod +x auto_deploy.sh
bash auto_deploy.sh
```

#### 📋 手动部署步骤

**1. 服务器环境准备**
```bash
# 服务器要求
- 操作系统: Linux (Ubuntu/CentOS)
- Node.js: v14.x (推荐版本)
- 开放端口: 3000
- 宝塔面板: 已安装
```

**2. 宝塔面板部署**
```bash
# 宝塔面板地址: http://47.95.213.220:8888

# 创建数据库
- 数据库名: oa_mysql
- 用户名: oa_user
- 密码: Yys2361878+
- 访问权限: 本地服务器

# 上传代码到 /www/wwwroot/oa-backend/
# 安装依赖: npm install
# 启动服务: pm2 start src/server.js --name oa-approval
```

**3. 配置说明**
- 服务器地址: `http://47.95.213.220:3000`
- 数据库: MySQL/SQLite (自动创建)
- 进程管理: PM2
- 默认管理员: admin/admin123

**4. Android连接配置**
```kotlin
// 生产环境API地址
private const val BASE_URL = "http://47.95.213.220:3000/api/"

// 网络安全配置 (network_security_config.xml)
<domain-config cleartextTrafficPermitted="true">
    <domain includeSubdomains="true">47.95.213.220</domain>
</domain-config>
```

## 🔧 配置说明

### 环境变量
```bash
NODE_ENV=production          # 环境模式
PORT=3000                   # 服务端口
JWT_SECRET=your-secret-key  # JWT密钥
UPLOAD_DIR=./uploads        # 上传目录
```

### Android配置
- **模拟器**: `http://********:3000/api/`
- **真机**: `http://192.168.x.x:3000/api/`
- **生产**: `http://47.95.213.220:3000/api/`

## 🧪 测试指南

### 后端测试
```bash
cd backend
node test_local_server.js    # 本地API测试
node test_db_connection.js   # 数据库连接测试
```

### Android测试
- 使用Android Studio的测试功能
- 检查网络连接和API响应
- 验证用户界面和交互逻辑

## 📝 开发规范

### 代码结构
- **Android**: 遵循MVVM架构模式
- **后端**: 遵循MVC架构模式
- **数据库**: 使用Sequelize ORM

### 命名规范
- **文件名**: 使用PascalCase (如: `UserRepository.kt`)
- **变量名**: 使用camelCase (如: `currentUser`)
- **常量名**: 使用UPPER_SNAKE_CASE (如: `BASE_URL`)

### 错误处理
- 统一使用Result类型处理异步操作结果
- 提供用户友好的错误提示
- 记录详细的调试日志

## 🔒 安全考虑

### 认证安全
- JWT令牌有效期管理
- 密码加密存储 (bcrypt)
- API访问权限控制

### 数据安全
- 输入数据验证
- SQL注入防护 (Sequelize ORM)
- 文件上传安全检查

### 网络安全
- HTTPS传输 (生产环境)
- CORS跨域配置
- 请求频率限制

## 📞 技术支持

### 常见问题
1. **连接失败**: 检查网络配置和服务器状态
2. **登录失败**: 验证用户名密码和token有效性
3. **数据同步**: 确认API响应格式和数据模型匹配

### 日志查看
```bash
# 后端日志
pm2 logs oa-approval

# Android日志
adb logcat | grep "OAApproval"
```

### 联系方式
- 项目维护: OA开发团队
- 技术支持: 查看项目Issues
- 文档更新: 参考各模块README文件

## 🔄 文件交互关系详解

### Android应用内部交互

```
MainActivity.kt
    ↓ 初始化
OAApplication.kt → ApiClient.init()
    ↓ 导航
OANavigationHost.kt
    ↓ 页面路由
LoginScreen → AuthViewModel → UserRepository → ApiService
    ↓ 成功登录
EmployeeHomeScreen → ApprovalViewModel → ApprovalRepository → ApiService
    ↓ 创建申请
CreateApprovalScreen → ApprovalViewModel.createApproval()
    ↓ API调用
ApiClient.apiService.createApproval() → 后端API
```

### 后端内部交互

```
server.js (启动服务器)
    ↓ 路由注册
routes/auth.routes.js → controllers/auth.controller.js
    ↓ 数据操作
models/index.js → models/user.model.js
    ↓ 数据库操作
Sequelize → database.sqlite
    ↓ 响应返回
JSON Response → Android客户端
```

### 关键配置文件交互

**1. 数据库配置流程**
```
db.config.js → 环境检测 → SQLite/MySQL选择
    ↓
models/index.js → 创建Sequelize实例
    ↓
server.js → 数据库同步 → 创建管理员用户
```

**2. API客户端配置流程**
```
ApiConfig.kt → 环境检测 → 模拟器/真机判断
    ↓
ApiClient.kt → 动态BASE_URL → Retrofit实例
    ↓
Repository层 → API调用 → 后端服务器
```

## 📊 性能优化

### Android应用优化
- **内存管理**: 使用StateFlow替代LiveData
- **网络优化**: OkHttp连接池和缓存
- **UI优化**: Jetpack Compose懒加载
- **数据缓存**: Repository层本地缓存

### 后端优化
- **数据库优化**: Sequelize查询优化
- **内存管理**: Node.js垃圾回收调优
- **并发处理**: Express.js中间件优化
- **文件处理**: 流式文件上传

## 🐛 故障排除

### 常见错误及解决方案

**1. Android编译错误**
```bash
# 问题: Unresolved reference 'ApiClient'
# 解决: 检查import语句
import com.oaapproval.system.data.api.ApiClient

# 问题: 依赖冲突
# 解决: 清理并重新构建
./gradlew clean build

# 问题: Kotlin编译错误
# 解决: 检查语法和类型匹配
```

**2. 网络连接问题**
```kotlin
// 问题: 模拟器网络连接超时
// 解决: 使用正确的模拟器地址
private const val BASE_URL = "http://********:3000/api/"

// 问题: 真机无法连接
// 解决: 确保设备与电脑在同一网络
private const val BASE_URL = "http://192.168.x.x:3000/api/"
```

**3. 后端服务问题**
```bash
# 问题: SQLite3 ELF Header错误
# 解决: 重新编译SQLite3
rm -rf node_modules
npm install
npm rebuild sqlite3

# 问题: 端口被占用
# 解决: 更换端口或杀死占用进程
lsof -ti:3000 | xargs kill -9
```

**4. 认证和Token问题**
```javascript
// 问题: JWT Token过期
// 解决: 自动刷新机制
if (error.response?.status === 401) {
    ApiClient.clearToken()
    // 重新登录
}

// 问题: Token格式错误
// 解决: 检查Token生成和验证逻辑
```

**5. 数据库问题**
```javascript
// 问题: 数据库连接失败
// 解决: 检查配置文件
console.log('数据库配置:', require('./src/config/db.config.js'))

// 问题: 表结构不匹配
// 解决: 重新初始化数据库
node src/scripts/init_database.js
```

**6. 协程和异步问题**
```kotlin
// 问题: Coroutine异常
// 解决: 正确的异常处理
viewModelScope.launch {
    try {
        // 异步操作
    } catch (e: Exception) {
        Log.e("Error", "操作失败", e)
    }
}
```

## 🔧 开发工具

### 推荐开发环境
- **Android**: Android Studio Arctic Fox+
- **后端**: VS Code + Node.js 14+
- **数据库**: SQLite Browser
- **API测试**: Postman/Insomnia
- **版本控制**: Git

### 调试工具
```bash
# 后端调试
npm run dev          # 开发模式启动
pm2 logs oa-approval # 查看生产日志

# Android调试
adb logcat           # 查看设备日志
./gradlew build      # 构建检查
```

## 📈 扩展计划

### 短期计划 (1-3个月)
- [ ] 🆕 **微信文件直接审批功能** (最高优先级)
- [ ] 添加推送通知功能
- [ ] 实现离线数据同步
- [ ] 优化UI/UX设计
- [ ] 增加单元测试覆盖率

### 中期计划 (3-6个月)
- [ ] 支持多租户架构
- [ ] 添加数据统计报表
- [ ] 实现工作流自定义
- [ ] 集成第三方认证

### 长期计划 (6-12个月)
- [ ] 微服务架构重构
- [ ] 支持多语言国际化
- [ ] 添加AI智能审批
- [ ] 云原生部署支持

## 📚 相关文档

### 技术文档
- [Android应用开发指南](./app/README.md) - Android客户端详细开发文档
- [后端API开发指南](./backend/README.md) - Node.js后端详细部署文档
- [设计原型文档](./design/README.md) - HTML原型设计说明

### 快速参考
- **本文档**: 完整的项目概览和使用指南
- **APK生成**: 参考上方"APK生成指南"章节
- **部署指南**: 参考上方"部署指南"章节
- **故障排除**: 参考上方"故障排除"章节

## 🤝 贡献指南

### 代码贡献
1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

### 问题反馈
- 使用GitHub Issues报告bug
- 提供详细的错误信息和复现步骤
- 包含系统环境和版本信息

---

## 📋 版本历史

### v2.1.0 (计划中 - 2025-01-25)
- 🚀 **重大优化**: 数据分层管理架构
  - 📊 **三层数据架构**: 热数据(内存) → 温数据(本地DB) → 冷数据(云端)
  - ⚡ **性能提升**: 响应速度提升80%，内存使用减少70%
  - 🔄 **增量同步**: 减少90%网络流量，支持离线访问
  - 🧠 **智能缓存**: 基于用户行为的预测性数据加载
- 🆕 **发起阅读功能**: 全新的文档阅读分享功能
  - 📚 **阅读任务**: 创建和管理阅读任务
  - 👥 **批量通知**: 一次性通知多个阅读对象
  - 📊 **状态跟踪**: 实时跟踪阅读进度和完成率
  - 💬 **反馈收集**: 收集阅读反馈和意见
- 🆕 **微信文件直接审批**: 支持从微信直接发起审批流程
- 📱 **用户体验**: 即时响应，流畅操作，智能预加载

### v2.0.0 (2025-01-18)
- 🗑️ **重大更新**: 删除审批类型功能，统一审批流程
- 🔧 **架构优化**: 简化数据模型，提升系统性能
- 🎨 **UI优化**: 删除类型相关UI，界面更加简洁
- 🐛 **问题修复**: 修复测试编码显示问题

### v1.5.0 (2025-01-15)
- 🔄 **流转功能**: 全面修复流转显示和权限控制
- 📝 **审批流程**: 优化审批步骤显示和状态同步
- 💾 **数据持久化**: 增强本地缓存和离线同步
- 📎 **附件管理**: 完善附件上传和预览功能

### v1.0.0 (2025-01-01)
- 🎉 **首次发布**: 基础审批系统功能
- 👤 **用户管理**: 登录注册、权限控制
- 📋 **审批流程**: 创建申请、多级审批
- 🔧 **基础架构**: Android + Node.js + SQLite

**最后更新**: 2025年1月18日
**当前版本**: v2.0.0 → v2.1.0 (开发中)
**状态**: 生产就绪 ✅ → 性能优化中 🚀
**维护团队**: OA开发团队

## 🚀 数据分层管理优化方案

### 📊 问题背景
随着审批记录数量的增长，系统面临以下挑战：
- **服务器压力**：大量历史数据查询导致响应缓慢
- **内存占用**：客户端加载过多数据导致卡顿
- **网络流量**：重复下载相同数据浪费带宽
- **用户体验**：加载时间过长影响使用体验

### 🌡️ 数据分层策略

#### 1. **热数据（Hot Data）- 内存缓存**
```kotlin
// 存储位置：内存缓存 (RAM)
// 访问频率：极高 (每分钟多次)
// 响应时间：< 10ms
// 数据范围：当前使用的数据

class HotDataManager {
    // 当前正在查看的审批详情
    private val currentApprovalCache = mutableMapOf<String, Approval>()

    // 今日审批列表（最多50条）
    private val todayApprovalsCache = LRUCache<String, List<Approval>>(1)

    // 用户最近操作的审批（最多20条）
    private val recentOperationsCache = LRUCache<String, Approval>(20)

    // 缓存有效期：5分钟
    private const val HOT_CACHE_DURATION = 5 * 60 * 1000L
}
```

#### 2. **温数据（Warm Data）- 本地数据库**
```kotlin
// 存储位置：本地SQLite数据库
// 访问频率：中等 (每天几次)
// 响应时间：< 100ms
// 数据范围：最近30天的数据

@Entity(tableName = "local_approvals")
data class LocalApprovalEntity(
    @PrimaryKey val id: String,
    val title: String,
    val content: String,
    val status: ApprovalStatus,
    val createTime: Long,
    val updateTime: Long,
    // 数据温度标记
    val dataTemperature: DataTemperature = DataTemperature.WARM,
    // 最后访问时间
    val lastAccessTime: Long = System.currentTimeMillis(),
    // 访问次数
    val accessCount: Int = 0
)

enum class DataTemperature {
    HOT,    // 热数据：当前使用
    WARM,   // 温数据：近期可能使用
    COLD    // 冷数据：很少使用
}
```

#### 3. **冷数据（Cold Data）- 云端存储**
```kotlin
// 存储位置：云端服务器
// 访问频率：极低 (几个月一次)
// 响应时间：1-3秒
// 数据范围：30天以前的历史数据

class ColdDataManager {
    // 冷数据的定义标准
    companion object {
        const val COLD_DATA_AGE_DAYS = 30         // 30天前的数据
        const val COLD_DATA_ACCESS_THRESHOLD = 2  // 访问次数少于2次
    }

    suspend fun moveDataToCold() {
        val coldDataCandidates = localDao.findColdDataCandidates(
            olderThan = System.currentTimeMillis() - (COLD_DATA_AGE_DAYS * 24 * 60 * 60 * 1000),
            accessCountLessThan = COLD_DATA_ACCESS_THRESHOLD
        )

        // 上传到云端并删除本地副本
        coldDataCandidates.forEach { approval ->
            cloudStorage.uploadApproval(approval)
            localDao.delete(approval.id)

            // 保留索引信息用于搜索
            localDao.insertIndex(ApprovalIndex(
                id = approval.id,
                title = approval.title,
                createTime = approval.createTime,
                isInCloud = true
            ))
        }
    }
}
```

### 🎯 实施方案

#### 阶段一：优化现有缓存（立即实施）
```kotlin
// 1. 扩展缓存时间和容量
private const val CACHE_DURATION = 30 * 60 * 1000L // 从2分钟增加到30分钟
private const val MAX_CACHE_SIZE = 1000 // 最多缓存1000条记录

// 2. 智能缓存策略
class SmartCacheManager {
    private val recentApprovals = LRUCache<String, Approval>(100)
    private val frequentApprovals = LRUCache<String, Approval>(200)

    fun getApproval(id: String): Approval? {
        return recentApprovals.get(id) ?: frequentApprovals.get(id)
    }
}

// 3. 分页加载机制
class PaginatedApprovalRepository {
    private val pageSize = 20

    suspend fun loadApprovals(page: Int): List<Approval> {
        // 先从本地加载
        val localData = localDao.getApprovalsPaged(pageSize, page * pageSize)

        if (localData.isNotEmpty()) {
            // 后台静默更新
            backgroundSync(page)
            return localData.map { it.toApproval() }
        }

        // 本地没有数据时从服务器加载
        return remoteApi.getApprovals(page, pageSize)
    }
}
```

#### 阶段二：引入本地数据库（1-2周内）
```kotlin
// 1. Room数据库配置
@Database(
    entities = [LocalApprovalEntity::class, ApprovalIndex::class],
    version = 1,
    exportSchema = false
)
abstract class LocalApprovalDatabase : RoomDatabase() {
    abstract fun approvalDao(): LocalApprovalDao
    abstract fun indexDao(): ApprovalIndexDao
}

// 2. 数据访问对象
@Dao
interface LocalApprovalDao {
    @Query("SELECT * FROM local_approvals ORDER BY createTime DESC LIMIT :limit OFFSET :offset")
    suspend fun getApprovalsPaged(limit: Int, offset: Int): List<LocalApprovalEntity>

    @Query("DELETE FROM local_approvals WHERE lastAccessTime < :expireTime")
    suspend fun cleanExpiredApprovals(expireTime: Long)

    @Query("SELECT * FROM local_approvals WHERE createTime > :startTime AND accessCount < :threshold")
    suspend fun findColdDataCandidates(startTime: Long, threshold: Int): List<LocalApprovalEntity>
}

// 3. 增量同步机制
class IncrementalSyncManager {
    suspend fun syncApprovals() {
        val lastSyncTime = preferences.getLastSyncTime()

        // 只获取变更的数据
        val changes = api.getApprovalChanges(lastSyncTime)

        changes.forEach { change ->
            when (change.type) {
                ChangeType.CREATE -> localDao.insert(change.approval)
                ChangeType.UPDATE -> localDao.update(change.approval)
                ChangeType.DELETE -> localDao.delete(change.approvalId)
            }
        }

        preferences.setLastSyncTime(System.currentTimeMillis())
    }
}
```

#### 阶段三：完整分层架构（2-4周内）
```kotlin
// 1. 统一数据访问层
class LayeredDataRepository {

    suspend fun getApproval(id: String): Approval? {
        // 第一层：热数据（内存缓存）
        hotDataManager.getCachedApproval(id)?.let { return it }

        // 第二层：温数据（本地数据库）
        localDao.getApproval(id)?.let { approval ->
            // 更新访问记录
            localDao.updateAccessInfo(id, System.currentTimeMillis())
            // 加入热缓存
            hotDataManager.cacheApproval(approval)
            return approval.toApproval()
        }

        // 第三层：冷数据（云端服务器）
        val index = indexDao.getApprovalIndex(id)
        if (index?.isInCloud == true) {
            showLoading("正在从服务器获取历史数据...")
            val approval = cloudApi.getApproval(id)
            // 缓存到本地
            localDao.insert(approval.toLocalEntity())
            return approval
        }

        return null
    }

    suspend fun getApprovalList(page: Int): List<Approval> {
        // 智能数据源选择
        return when {
            page == 0 -> getRecentApprovals() // 最新数据优先从热缓存
            page <= 5 -> getWarmApprovals(page) // 近期数据从本地数据库
            else -> getColdApprovals(page) // 历史数据从云端
        }
    }
}

// 2. 自动数据迁移
class DataMigrationManager {

    // 每日凌晨执行数据迁移
    @Scheduled(cron = "0 0 2 * * ?")
    suspend fun dailyDataMigration() {
        // 热数据 → 温数据
        moveHotToWarm()

        // 温数据 → 冷数据
        moveWarmToCold()

        // 清理过期数据
        cleanExpiredData()
    }

    private suspend fun moveHotToWarm() {
        val expiredHotData = hotDataManager.getExpiredData()
        expiredHotData.forEach { approval ->
            localDao.insert(approval.toLocalEntity())
            hotDataManager.remove(approval.id)
        }
    }

    private suspend fun moveWarmToCold() {
        val coldDataCandidates = localDao.findColdDataCandidates(
            olderThan = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000),
            accessCountLessThan = 2
        )

        coldDataManager.moveDataToCold(coldDataCandidates)
    }
}
```

### 📈 性能优化效果

#### 预期性能提升
```kotlin
// 优化前 vs 优化后对比
class PerformanceMetrics {

    // 数据加载时间
    val loadingTime = mapOf(
        "优化前" to mapOf(
            "首页加载" to "3-5秒",
            "历史记录" to "5-10秒",
            "详情页面" to "2-3秒"
        ),
        "优化后" to mapOf(
            "首页加载" to "0.5-1秒",   // 提升80%
            "历史记录" to "1-2秒",     // 提升75%
            "详情页面" to "0.1-0.5秒"  // 提升85%
        )
    )

    // 内存使用
    val memoryUsage = mapOf(
        "优化前" to "100-200MB",
        "优化后" to "30-60MB"  // 减少70%
    )

    // 网络流量
    val networkTraffic = mapOf(
        "优化前" to "每次全量下载",
        "优化后" to "增量同步，减少90%"
    )

    // 服务器压力
    val serverLoad = mapOf(
        "优化前" to "每次查询数据库",
        "优化后" to "缓存命中率85%+"
    )
}
```

#### 用户体验改善
- **即时响应**：常用数据瞬间加载
- **流畅操作**：减少等待时间
- **离线支持**：本地数据可离线访问
- **智能预加载**：根据使用习惯预测需要的数据

### 🔧 技术实现细节

#### 1. 数据温度判断算法
```kotlin
class DataTemperatureCalculator {

    fun calculateTemperature(approval: Approval): DataTemperature {
        val now = System.currentTimeMillis()
        val daysSinceCreated = (now - approval.createTime) / (24 * 60 * 60 * 1000)
        val daysSinceAccessed = (now - approval.lastAccessTime) / (24 * 60 * 60 * 1000)

        return when {
            // 热数据：今天创建或今天访问过
            daysSinceCreated <= 1 || daysSinceAccessed <= 1 -> DataTemperature.HOT

            // 温数据：7天内创建或7天内访问过
            daysSinceCreated <= 7 || daysSinceAccessed <= 7 -> DataTemperature.WARM

            // 冷数据：超过7天且访问次数少
            else -> DataTemperature.COLD
        }
    }
}
```

#### 2. 智能缓存策略
```kotlin
class IntelligentCacheStrategy {

    // 基于用户行为的预加载
    suspend fun predictivePreload(userId: String) {
        val userBehavior = analyzeUserBehavior(userId)

        // 预加载用户常用的审批类型
        userBehavior.frequentApprovalTypes.forEach { type ->
            preloadApprovalsByType(type)
        }

        // 预加载用户关注的审批人数据
        userBehavior.frequentApprovers.forEach { approverId ->
            preloadApprovalsByApprover(approverId)
        }
    }

    // 基于时间模式的预加载
    suspend fun timeBasedPreload() {
        val currentHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY)

        when (currentHour) {
            in 9..11 -> preloadMorningData()    // 上午：预加载待审批
            in 14..16 -> preloadAfternoonData() // 下午：预加载历史记录
            in 19..21 -> preloadEveningData()   // 晚上：预加载统计数据
        }
    }
}
```

#### 3. 数据一致性保证
```kotlin
class DataConsistencyManager {

    // 多层数据同步
    suspend fun syncAllLayers() {
        // 1. 云端 → 本地数据库
        syncCloudToLocal()

        // 2. 本地数据库 → 内存缓存
        syncLocalToMemory()

        // 3. 检查数据一致性
        validateDataConsistency()
    }

    // 冲突解决策略
    suspend fun resolveConflicts(conflicts: List<DataConflict>) {
        conflicts.forEach { conflict ->
            when (conflict.type) {
                ConflictType.VERSION_MISMATCH -> {
                    // 以服务器版本为准
                    updateLocalData(conflict.serverId, conflict.serverData)
                }
                ConflictType.DELETED_LOCALLY -> {
                    // 检查服务器是否也删除
                    if (checkServerDeleted(conflict.id)) {
                        removeFromAllLayers(conflict.id)
                    } else {
                        restoreLocalData(conflict.id, conflict.serverData)
                    }
                }
            }
        }
    }
}
```

### 🎯 实施时间表

#### 第1周：基础优化
- [x] 扩展现有缓存时间和容量
- [x] 实现分页加载机制
- [x] 添加智能缓存策略

#### 第2-3周：本地数据库
- [ ] 设计Room数据库架构
- [ ] 实现数据访问层
- [ ] 添加增量同步机制

#### 第4-5周：完整分层架构
- [ ] 实现三层数据管理
- [ ] 添加自动数据迁移
- [ ] 完善数据一致性保证

#### 第6周：测试和优化
- [ ] 性能测试和调优
- [ ] 用户体验测试
- [ ] 文档完善和培训

### 💡 关键优势

#### 1. **性能提升**
- **响应速度**：热数据毫秒级响应
- **内存优化**：减少70%内存占用
- **网络优化**：减少90%重复请求

#### 2. **用户体验**
- **无感知加载**：常用数据即时显示
- **离线支持**：本地数据离线可用
- **智能预测**：提前加载可能需要的数据

#### 3. **系统稳定性**
- **服务器减压**：大幅减少数据库查询
- **容错能力**：多层备份保证数据安全
- **扩展性**：支持海量历史数据

#### 4. **成本控制**
- **存储成本**：本地只保存必要数据
- **带宽成本**：增量同步减少流量
- **服务器成本**：减少计算资源消耗

这个分层管理方案将彻底解决审批记录过多导致的性能问题，同时为系统的长期发展奠定坚实基础。

## 🎯 下一步计划

### 即将发布 (v2.1.0)
- [ ] 🆕 **数据分层管理优化** (最高优先级)
  - [ ] 阶段一：优化现有缓存
  - [ ] 阶段二：引入本地数据库
  - [ ] 阶段三：完整分层架构
- [ ] 🆕 **发起阅读功能** (高优先级)
  - [ ] 创建全新的阅读功能数据库表结构
  - [ ] 开发独立的阅读功能API接口
  - [ ] 新建CreateReadingScreen发起阅读界面
  - [ ] 新建阅读列表和详情界面
  - [ ] 开发ReadingViewModel和Repository
  - [ ] 测试完整阅读功能流程
- [ ] 🆕 **微信文件直接审批功能** (中优先级)
  - [ ] PDF文件支持
  - [ ] WPS文档支持
  - [ ] 图片文件支持
  - [ ] Intent Filter配置 (ACTION_VIEW)
  - [ ] 文件接收处理逻辑
  - [ ] 自动审批流程触发
  - [ ] 微信兼容性测试
- [ ] 推送通知功能
- [ ] 数据统计报表
- [ ] 工作流自定义

### 长期规划
- [ ] 微服务架构
- [ ] 多语言支持
- [ ] AI智能审批
- [ ] 云原生部署
