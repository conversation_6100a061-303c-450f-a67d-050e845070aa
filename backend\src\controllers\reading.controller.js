const db = require("../models");
const Reading = db.reading;
const ReadingStep = db.readingStep;
const ReadingComment = db.readingComment;
const ReadingAttachment = db.readingAttachment;
const User = db.user;

// 获取当前时间字符串
function getLocalTimeString() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 创建阅读任务
exports.createReading = async (req, res) => {
  try {
    console.log('创建阅读请求 - 请求体:', req.body);
    console.log('创建阅读请求 - 用户ID:', req.userId);

    // 验证当前用户是否存在
    const currentUser = await User.findByPk(req.userId);
    if (!currentUser) {
      console.error('创建阅读 - 当前用户不存在:', req.userId);
      return res.status(400).send({
        message: "当前用户不存在"
      });
    }

    // 验证请求
    if (!req.body.title || req.body.title.trim() === '') {
      return res.status(400).send({
        message: "标题不能为空"
      });
    }

    if (!req.body.readerIds || !Array.isArray(req.body.readerIds) || req.body.readerIds.length === 0) {
      return res.status(400).send({
        message: "必须指定至少一个阅读对象"
      });
    }

    // 验证和清理输入数据
    const title = req.body.title.trim();
    const description = req.body.description || '';
    const priority = req.body.priority || 'NORMAL';

    console.log('创建阅读 - 输入验证:', {
      title: title,
      descriptionLength: description.length,
      userId: req.userId,
      readerIds: req.body.readerIds,
      priority: priority
    });

    // 创建阅读任务
    const reading = {
      title: title,
      description: description,
      priority: priority,
      creatorId: req.userId,
      status: 'PENDING',
      submitTime: getLocalTimeString()
    };

    console.log('创建阅读 - 准备创建阅读任务:', reading);

    const createdReading = await Reading.create(reading);
    console.log('创建阅读 - 阅读任务创建成功:', createdReading.id);

    // 验证阅读对象是否存在
    const readerIds = req.body.readerIds.map(id => String(id));
    const existingUsers = await User.findAll({
      where: {
        id: readerIds
      },
      attributes: ['id', 'realName', 'position']
    });

    console.log('创建阅读 - 找到的阅读对象信息:', existingUsers.map(u => ({ id: u.id, realName: u.realName, position: u.position })));

    if (existingUsers.length !== readerIds.length) {
      const existingIds = existingUsers.map(u => u.id);
      const missingIds = readerIds.filter(id => !existingIds.includes(id));
      return res.status(400).send({
        message: `阅读对象不存在: ${missingIds.join(', ')}`,
        missingReaders: missingIds
      });
    }

    // 创建阅读步骤 - 所有阅读对象都可以同时阅读
    const readingSteps = readerIds.map((readerId) => ({
      readingId: createdReading.id,
      readerId: readerId,
      status: "UNREAD"
    }));

    console.log('创建阅读 - 准备创建阅读步骤:', readingSteps.length, '个步骤');

    if (readingSteps.length > 0) {
      await ReadingStep.bulkCreate(readingSteps);
      console.log('创建阅读 - 阅读步骤创建成功');
    }

    // 返回创建成功的响应
    res.status(201).send({
      message: "阅读任务创建成功!",
      readingId: createdReading.id,
      reading: {
        id: createdReading.id,
        title: createdReading.title,
        description: createdReading.description,
        priority: createdReading.priority,
        status: createdReading.status,
        submitTime: createdReading.submitTime,
        creator: {
          id: currentUser.id,
          realName: currentUser.realName,
          position: currentUser.position
        }
      }
    });

  } catch (error) {
    console.error('创建阅读失败:', error);
    res.status(500).send({
      message: "创建阅读时发生错误",
      error: error.message
    });
  }
};

// 获取阅读列表
exports.getAllReadings = async (req, res) => {
  try {
    const readings = await Reading.findAll({
      include: [
        {
          model: User,
          as: "creator",
          attributes: ['id', 'realName', 'position']
        },
        {
          model: ReadingStep,
          as: "steps",
          include: [{
            model: User,
            as: "reader",
            attributes: ['id', 'realName', 'position']
          }]
        }
      ],
      order: [['submitTime', 'DESC']]
    });

    res.status(200).send(readings);
  } catch (error) {
    console.error('获取阅读列表失败:', error);
    res.status(500).send({
      message: "获取阅读列表时发生错误",
      error: error.message
    });
  }
};

// 获取我发起的阅读
exports.getMyReadings = async (req, res) => {
  try {
    const readings = await Reading.findAll({
      where: {
        creatorId: req.userId
      },
      include: [
        {
          model: User,
          as: "creator",
          attributes: ['id', 'realName', 'position']
        },
        {
          model: ReadingStep,
          as: "steps",
          include: [{
            model: User,
            as: "reader",
            attributes: ['id', 'realName', 'position']
          }]
        }
      ],
      order: [['submitTime', 'DESC']]
    });

    res.status(200).send(readings);
  } catch (error) {
    console.error('获取我的阅读失败:', error);
    res.status(500).send({
      message: "获取我的阅读时发生错误",
      error: error.message
    });
  }
};

// 获取待我阅读的任务
exports.getPendingReadings = async (req, res) => {
  try {
    const readingSteps = await ReadingStep.findAll({
      where: {
        readerId: req.userId,
        status: 'UNREAD'
      },
      include: [
        {
          model: Reading,
          include: [{
            model: User,
            as: "creator",
            attributes: ['id', 'realName', 'position']
          }]
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    const readings = readingSteps.map(step => step.reading);
    res.status(200).send(readings);
  } catch (error) {
    console.error('获取待阅读任务失败:', error);
    res.status(500).send({
      message: "获取待阅读任务时发生错误",
      error: error.message
    });
  }
};

// 获取阅读详情
exports.getReadingDetail = async (req, res) => {
  try {
    const reading = await Reading.findByPk(req.params.id, {
      include: [
        {
          model: User,
          as: "creator",
          attributes: ['id', 'realName', 'position']
        },
        {
          model: ReadingStep,
          as: "steps",
          include: [{
            model: User,
            as: "reader",
            attributes: ['id', 'realName', 'position']
          }]
        },
        {
          model: ReadingComment,
          as: "comments",
          include: [{
            model: User,
            as: "commenter",
            attributes: ['id', 'realName', 'position']
          }],
          order: [['commentTime', 'ASC']]
        },
        {
          model: ReadingAttachment,
          as: "attachments"
        }
      ]
    });

    if (!reading) {
      return res.status(404).send({
        message: "未找到阅读任务!"
      });
    }

    res.status(200).send(reading);
  } catch (error) {
    console.error('获取阅读详情失败:', error);
    res.status(500).send({
      message: "获取阅读详情时发生错误",
      error: error.message
    });
  }
};

// 确认已读
exports.confirmReading = async (req, res) => {
  try {
    // 查找阅读任务
    const reading = await Reading.findByPk(req.params.id);
    if (!reading) {
      return res.status(404).send({
        message: "未找到阅读任务!"
      });
    }

    // 查找当前用户的阅读步骤
    const userStep = await ReadingStep.findOne({
      where: {
        readingId: req.params.id,
        readerId: req.userId,
        status: 'UNREAD'
      }
    });

    if (!userStep) {
      return res.status(400).send({
        message: "您没有权限确认此阅读任务或已经确认过了"
      });
    }

    // 更新阅读步骤状态
    await userStep.update({
      status: "READ",
      readTime: getLocalTimeString(),
      comment: req.body.comment || ""
    });

    // 如果用户添加了意见，创建意见记录
    const comment = req.body.comment;
    if (comment && comment.trim() !== "") {
      await ReadingComment.create({
        readingId: req.params.id,
        commenterId: req.userId,
        content: comment,
        commentTime: getLocalTimeString()
      });
    }

    // 检查是否所有人都已阅读完成
    const allSteps = await ReadingStep.findAll({
      where: { readingId: req.params.id }
    });

    const allRead = allSteps.every(step => step.status === 'READ');
    if (allRead) {
      await reading.update({ status: 'COMPLETED' });
    }

    res.status(200).send({
      message: "确认已读成功!",
      reading: {
        ...reading.toJSON(),
        status: allRead ? "COMPLETED" : "PENDING"
      }
    });

  } catch (error) {
    console.error('确认已读失败:', error);
    res.status(500).send({
      message: "确认已读时发生错误",
      error: error.message
    });
  }
};

// 添加阅读意见
exports.addComment = async (req, res) => {
  try {
    // 验证请求
    if (!req.body.content) {
      return res.status(400).send({
        message: "意见内容不能为空"
      });
    }
    
    // 查找阅读任务
    const reading = await Reading.findByPk(req.params.id);
    if (!reading) {
      return res.status(404).send({
        message: "未找到阅读任务!"
      });
    }
    
    // 创建阅读意见
    const comment = await ReadingComment.create({
      readingId: req.params.id,
      commenterId: req.userId,
      content: req.body.content,
      commentTime: getLocalTimeString()
    });
    
    // 查询完整意见信息（包括评论人）
    const completeComment = await ReadingComment.findByPk(comment.id, {
      include: [{
        model: User,
        as: "commenter",
        attributes: ['id', 'realName', 'position']
      }]
    });
    
    res.status(201).send({
      message: "阅读意见添加成功!",
      comment: completeComment
    });

  } catch (error) {
    console.error('添加阅读意见失败:', error);
    res.status(500).send({
      message: "添加阅读意见时发生错误",
      error: error.message
    });
  }
};
