const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const db = require('./models');
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const approvalRoutes = require('./routes/approval.routes');
const readingRoutes = require('./routes/reading.routes');
const fs = require('fs');

// 导入SSE通知服务
const NotificationService = require('./services/notification.service');

// 加载环境变量
dotenv.config();

const app = express();

// 创建uploads目录（如果不存在）
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 配置环境变量
if (!process.env.UPLOAD_DIR) {
  process.env.UPLOAD_DIR = uploadsDir;
}

// 配置服务器URL
if (!process.env.SERVER_URL) {
  process.env.SERVER_URL = 'http://47.95.213.220:3000';
}

// 中间件
app.use(cors());
// 增加请求体大小限制
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 添加详细的请求日志
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 设置日志文件
const accessLogStream = fs.createWriteStream(
  path.join(logDir, 'access.log'),
  { flags: 'a' }
);

// 配置静态文件访问 - 重要：允许直接访问上传的文件
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 静态文件服务
app.use(express.static(path.join(__dirname, '../public')));

// 测试接口（不需要认证）
app.get('/api/test', (req, res) => {
  res.json({ message: "测试接口正常，无需认证" });
});

// 健康检查接口
app.get('/api/health', async (req, res) => {
  try {
    // 检查数据库连接
    await db.sequelize.authenticate();
    res.json({
      status: "healthy",
      database: "connected",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: "unhealthy",
      database: "disconnected",
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 调试接口：检查附件状态
app.get('/api/debug/attachments/:approvalId', async (req, res) => {
  try {
    const { approvalId } = req.params;
    const db = require('./models');
    const Attachment = db.attachment;
    const path = require('path');
    const fs = require('fs');

    // 获取数据库中的附件记录
    const attachments = await Attachment.findAll({
      where: { approval_id: approvalId }
    });

    // 获取文件系统中的文件
    const uploadDir = path.join(__dirname, '../uploads');
    let files = [];
    try {
      files = fs.readdirSync(uploadDir);
    } catch (err) {
      files = [`错误: ${err.message}`];
    }

    res.json({
      approvalId,
      uploadDir,
      attachments: attachments.map(att => ({
        id: att.id,
        file_name: att.file_name,
        file_path: att.file_path,
        file_type: att.file_type,
        file_size: att.file_size
      })),
      filesInDirectory: files
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// 预览附件路由（不需要认证，放在其他路由之前）
const attachmentController = require('./controllers/attachment.controller');
app.get('/api/approvals/:approvalId/attachments/:attachmentId/preview', attachmentController.previewAttachment);

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/approvals', approvalRoutes);
app.use('/api/reading', readingRoutes);

// SSE通知路由
const notificationRoutes = require('./routes/notification.routes');
app.use('/api/notifications', notificationRoutes);

// 首页路由
app.get('/api', (req, res) => {
  res.json({ message: 'Welcome to OA Approval System API' });
});

// 将根路径请求重定向到API信息页面
app.get('/', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>OA审批系统API服务</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
          .container { max-width: 800px; margin: 0 auto; }
          h1 { color: #333; }
          .message { background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 20px 0; }
          .footer { margin-top: 30px; font-size: 0.9em; color: #6c757d; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>OA审批系统API服务</h1>
          <div class="message">
            <p>Web前端页面已移除，请使用Android应用访问系统。</p>
            <p>API基础URL: <code>http://${req.headers.host}/api</code></p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} OA审批系统</p>
          </div>
        </div>
      </body>
    </html>
  `);
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(`错误发生在 [${req.method}] ${req.path}:`, err);

  // 将错误写入错误日志文件
  const errorLogStream = fs.createWriteStream(
    path.join(logDir, 'error.log'),
    { flags: 'a' }
  );
  
  const errorLog = `[${new Date().toISOString()}] ${req.method} ${req.path} - ${err.stack || err}\n`;
  errorLogStream.write(errorLog);
  
  // 根据环境返回不同详细程度的错误信息
  const isDev = process.env.NODE_ENV !== 'production';
  
  // 返回错误响应
  res.status(err.status || 500).json({
    message: err.message || '服务器内部错误',
    error: isDev ? {
      stack: err.stack,
      details: err.details || null
    } : undefined
  });
});

// 404处理
app.use((req, res) => {
  console.log(`[${new Date().toISOString()}] 404错误: ${req.method} ${req.url}`);
  res.status(404).send({
    message: '未找到请求的资源'
  });
});

// 设置端口
const PORT = process.env.PORT || 3000;

// 数据库同步 - 修复模型字段映射问题
// 确保所有表使用统一的 utf8mb4_general_ci 字符集
console.log('🔄 开始数据库同步...');
db.sequelize.sync({ force: false, alter: true })
  .then(async () => {
    console.log('✅ 数据库同步成功 - 表结构已更新');

    // 检查并添加 completed_at 字段
    try {
      await db.sequelize.query(`
        ALTER TABLE approvals
        ADD COLUMN completed_at VARCHAR(50) NULL
      `);
      console.log('✅ 已添加 completed_at 字段');
    } catch (err) {
      if (err.message.includes('Duplicate column name')) {
        console.log('✅ completed_at 字段已存在');
      } else {
        console.log('⚠️ 添加 completed_at 字段时出错:', err.message);
      }
    }

    // 不再自动创建系统管理员用户
    console.log('✅ 数据库同步完成，不创建默认管理员用户');
    console.log('🚀 服务器已准备就绪，可以处理请求');
  })
  .catch(err => {
    console.error('❌ 数据库同步失败:', err);
  });

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`OA审批系统服务器运行在端口 ${PORT}`);
  console.log(`服务器地址: http://47.95.213.220:${PORT}`);
  console.log(`API地址: http://47.95.213.220:${PORT}/api`);
});