module.exports = (sequelize, Sequelize) => {
  const ReadingComment = sequelize.define("reading_comment", {
    id: {
      type: Sequelize.STRING(36),
      primaryKey: true,
      defaultValue: Sequelize.UUIDV4
    },
    readingId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'reading_id'
    },
    commenterId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'commenter_id'
    },
    content: {
      type: Sequelize.TEXT,
      allowNull: false
    },
    commentTime: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'comment_time'
    }
  }, {
    tableName: 'reading_comments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return ReadingComment;
};
