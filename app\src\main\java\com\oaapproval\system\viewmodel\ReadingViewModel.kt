package com.oaapproval.system.viewmodel

import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oaapproval.system.data.model.Reading
import com.oaapproval.system.data.model.ReadingAttachment
import com.oaapproval.system.data.repository.ReadingRepository
import com.oaapproval.system.data.repository.UserRepository
import kotlinx.coroutines.launch

class ReadingViewModel : ViewModel() {
    private val readingRepository = ReadingRepository()
    private val userRepository = UserRepository()
    
    companion object {
        private const val TAG = "ReadingViewModel"
    }
    
    // UI状态
    var isLoading by mutableStateOf(false)
        private set
    
    var errorMessage by mutableStateOf<String?>(null)
        private set
    
    // 阅读列表
    var allReadings by mutableStateOf<List<Reading>>(emptyList())
        private set
    
    var myReadings by mutableStateOf<List<Reading>>(emptyList())
        private set
    
    var pendingReadings by mutableStateOf<List<Reading>>(emptyList())
        private set
    
    // 当前阅读详情
    var currentReading by mutableStateOf<Reading?>(null)
        private set
    
    // 创建阅读任务
    fun createReading(
        title: String,
        description: String?,
        priority: String,
        readerIds: List<String>,
        attachments: List<ReadingAttachment>? = null,
        onSuccess: (String) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            Log.d(TAG, "开始创建阅读任务 - 标题: $title, 阅读对象: $readerIds")
            
            try {
                val result = readingRepository.createReading(
                    title = title,
                    description = description,
                    priority = priority,
                    readerIds = readerIds,
                    attachments = attachments
                )
                
                if (result.isSuccess) {
                    val reading = result.getOrNull()
                    Log.d(TAG, "创建阅读任务成功: ${reading?.id}")
                    onSuccess(reading?.id ?: "")
                    // 刷新我的阅读列表
                    loadMyReadings()
                } else {
                    val error = result.exceptionOrNull()?.message ?: "创建阅读任务失败"
                    Log.e(TAG, "创建阅读任务失败: $error")
                    errorMessage = error
                    onError(error)
                }
            } catch (e: Exception) {
                val error = e.message ?: "创建阅读任务时发生未知错误"
                Log.e(TAG, "创建阅读任务异常", e)
                errorMessage = error
                onError(error)
            } finally {
                isLoading = false
            }
        }
    }
    
    // 获取所有阅读任务
    fun loadAllReadings() {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.getAllReadings()
                
                if (result.isSuccess) {
                    allReadings = result.getOrNull() ?: emptyList()
                    Log.d(TAG, "获取所有阅读任务成功: ${allReadings.size}个")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "获取阅读任务失败"
                    Log.e(TAG, "获取所有阅读任务失败: $error")
                    errorMessage = error
                }
            } catch (e: Exception) {
                val error = e.message ?: "获取阅读任务时发生未知错误"
                Log.e(TAG, "获取所有阅读任务异常", e)
                errorMessage = error
            } finally {
                isLoading = false
            }
        }
    }
    
    // 获取我发起的阅读
    fun loadMyReadings() {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.getMyReadings()
                
                if (result.isSuccess) {
                    myReadings = result.getOrNull() ?: emptyList()
                    Log.d(TAG, "获取我发起的阅读成功: ${myReadings.size}个")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "获取我的阅读失败"
                    Log.e(TAG, "获取我发起的阅读失败: $error")
                    errorMessage = error
                }
            } catch (e: Exception) {
                val error = e.message ?: "获取我的阅读时发生未知错误"
                Log.e(TAG, "获取我发起的阅读异常", e)
                errorMessage = error
            } finally {
                isLoading = false
            }
        }
    }
    
    // 获取待我阅读的任务
    fun loadPendingReadings() {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.getPendingReadings()
                
                if (result.isSuccess) {
                    pendingReadings = result.getOrNull() ?: emptyList()
                    Log.d(TAG, "获取待我阅读的任务成功: ${pendingReadings.size}个")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "获取待阅读任务失败"
                    Log.e(TAG, "获取待我阅读的任务失败: $error")
                    errorMessage = error
                }
            } catch (e: Exception) {
                val error = e.message ?: "获取待阅读任务时发生未知错误"
                Log.e(TAG, "获取待我阅读的任务异常", e)
                errorMessage = error
            } finally {
                isLoading = false
            }
        }
    }
    
    // 获取阅读详情
    fun loadReadingDetail(readingId: String) {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.getReadingDetail(readingId)
                
                if (result.isSuccess) {
                    currentReading = result.getOrNull()
                    Log.d(TAG, "获取阅读详情成功: ${currentReading?.title}")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "获取阅读详情失败"
                    Log.e(TAG, "获取阅读详情失败: $error")
                    errorMessage = error
                }
            } catch (e: Exception) {
                val error = e.message ?: "获取阅读详情时发生未知错误"
                Log.e(TAG, "获取阅读详情异常", e)
                errorMessage = error
            } finally {
                isLoading = false
            }
        }
    }
    
    // 确认已读
    fun confirmReading(
        readingId: String,
        comment: String? = null,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.confirmReading(readingId, comment)
                
                if (result.isSuccess) {
                    Log.d(TAG, "确认已读成功")
                    onSuccess()
                    // 刷新相关数据
                    loadPendingReadings()
                    loadReadingDetail(readingId)
                } else {
                    val error = result.exceptionOrNull()?.message ?: "确认已读失败"
                    Log.e(TAG, "确认已读失败: $error")
                    errorMessage = error
                    onError(error)
                }
            } catch (e: Exception) {
                val error = e.message ?: "确认已读时发生未知错误"
                Log.e(TAG, "确认已读异常", e)
                errorMessage = error
                onError(error)
            } finally {
                isLoading = false
            }
        }
    }
    
    // 添加阅读意见
    fun addReadingComment(
        readingId: String,
        content: String,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            isLoading = true
            errorMessage = null
            
            try {
                val result = readingRepository.addReadingComment(readingId, content)
                
                if (result.isSuccess) {
                    Log.d(TAG, "添加阅读意见成功")
                    onSuccess()
                    // 刷新阅读详情
                    loadReadingDetail(readingId)
                } else {
                    val error = result.exceptionOrNull()?.message ?: "添加阅读意见失败"
                    Log.e(TAG, "添加阅读意见失败: $error")
                    errorMessage = error
                    onError(error)
                }
            } catch (e: Exception) {
                val error = e.message ?: "添加阅读意见时发生未知错误"
                Log.e(TAG, "添加阅读意见异常", e)
                errorMessage = error
                onError(error)
            } finally {
                isLoading = false
            }
        }
    }
    
    // 清除错误信息
    fun clearError() {
        errorMessage = null
    }
    
    // 清除当前阅读详情
    fun clearCurrentReading() {
        currentReading = null
    }
}
