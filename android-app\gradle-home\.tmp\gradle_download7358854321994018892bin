{
  "formatVersion": "1.1",
  "component": {
    "group": "org.jetbrains.kotlin",
    "module": "kotlin-gradle-plugin-api",
    "version": "1.9.23",
    "attributes": {
      "org.gradle.status": "release"
    }
  },
  "createdBy": {
    "gradle": {
      "version": "8.2.1"
    }
  },
  "variants": [
    {
      "name": "sourcesElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "sources",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-sources.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-sources.jar",
          "size": 54688,
          "sha512": "fb315f7fac2468f7a52843584c7d773cb6053ac1cfacb46fea546209e74618ca5bf117d17c39cc9f41008e8b2542f708879dff9f4fbbfa58c3094141c135aa4d",
          "sha256": "a0e8fdbe178482ba7f2b1ae16b31fc05075a969912d44484ceee42bda9c0b368",
          "sha1": "d99a3f177fd2ec8dbc2482f1b2d345121069e4ca",
          "md5": "73409ace78ef9153881c89abc0cc55c9"
        }
      ]
    },
    {
      "name": "javadocElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "javadoc",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-javadoc.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-javadoc.jar",
          "size": 929732,
          "sha512": "cc356c2e1becc10c322288d1df81f3a49cc45794575d7cd4e529cad910a9a06911620fd61142f24da0b75527903b4719e47c58f02e8edc966f01874153ad6fcb",
          "sha256": "73bf597f61347a18f573a13fba38a1405c1843d85f5d4e5b6c4c81b430237c1c",
          "sha1": "5da634e4f1d04410e943e197619520aa59e3aa3a",
          "md5": "c2030958bad42d59e32acc84b88f99eb"
        }
      ]
    },
    {
      "name": "runtimeElementsWithFixedAttribute",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.usage": "java-runtime"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ]
    },
    {
      "name": "apiElementsWithFixedAttribute",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.usage": "java-api"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ]
    },
    {
      "name": "gradle70JavadocElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "javadoc",
        "org.gradle.plugin.api-version": "7.0",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle70-javadoc.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle70-javadoc.jar",
          "size": 929712,
          "sha512": "e555aadad93016e423d2c6cc24ef5e4b90e3680fd46bf52ec1bde8627b6737254fa20c949f5f81cc6c80fe6eb2b28baf4deff0b717d813811d6ce8e2d49603c7",
          "sha256": "f50342fbecd1ac16a756a047413b0c33b76b07c4f111fddc9451cd3ee8a7d1b8",
          "sha1": "858ddf542db7f12fa8c7370a44a96ae6ebaa9e4b",
          "md5": "1854eedbef687b82ceebf6952c641de5"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle70",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle70SourcesElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "sources",
        "org.gradle.plugin.api-version": "7.0",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle70-sources.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle70-sources.jar",
          "size": 58933,
          "sha512": "e63963303f9a89eb73330360d269f3f60e7806a535efa935d05993f3054ad212aef8edc72776a234985b836e83d023845d9e100865eed8d465647e1bce6978eb",
          "sha256": "d4dd401142bb732699d42ce9bb8dac88b4e548c0c9563a0e120a74b409331563",
          "sha1": "7f145d52cd9f88cd75eab4fc2afd57741436d67c",
          "md5": "7021853e5c977f0c971918f7d637454e"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle70",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle70ApiElements",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.plugin.api-version": "7.0",
        "org.gradle.usage": "java-api"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle70.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle70.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle70",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle70RuntimeElements",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.plugin.api-version": "7.0",
        "org.gradle.usage": "java-runtime"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle70.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle70.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle70",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle71JavadocElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "javadoc",
        "org.gradle.plugin.api-version": "7.1",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle71-javadoc.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle71-javadoc.jar",
          "size": 929720,
          "sha512": "97ab8e10bea2d2ac51b6666c5a04f003ed080b46962d8aafcc7c898d309430530805b57f7ab8a26abce89e5cbe3cf2d73f9c5aba4fbde00cc3482131df7d6ad0",
          "sha256": "af38f1f8c385ae73f7507a5d7fb53b1cdcb8eade8f31d982233caac88ec44765",
          "sha1": "89ea860612b2560e7b9de4af493737c518dfe8c7",
          "md5": "9b76875bdb3e80ca1efafa9f13838046"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle71",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle71SourcesElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "sources",
        "org.gradle.plugin.api-version": "7.1",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle71-sources.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle71-sources.jar",
          "size": 58933,
          "sha512": "e63963303f9a89eb73330360d269f3f60e7806a535efa935d05993f3054ad212aef8edc72776a234985b836e83d023845d9e100865eed8d465647e1bce6978eb",
          "sha256": "d4dd401142bb732699d42ce9bb8dac88b4e548c0c9563a0e120a74b409331563",
          "sha1": "7f145d52cd9f88cd75eab4fc2afd57741436d67c",
          "md5": "7021853e5c977f0c971918f7d637454e"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle71",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle71ApiElements",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.plugin.api-version": "7.1",
        "org.gradle.usage": "java-api"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle71.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle71.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle71",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle71RuntimeElements",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.plugin.api-version": "7.1",
        "org.gradle.usage": "java-runtime"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle71.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle71.jar",
          "size": 238246,
          "sha512": "7a5b24b2c3c80c0eb9d125f4a324ad144e35ce4189519f9ae99142477a5b9c5ff5b12cfa543429133a401463fd70e4e4fd8c800e5d25e930a908a42703c8449b",
          "sha256": "593cc0842d5fc097b95e0a602bff8c7a489f73743b873cb0b8eec92fce8a415b",
          "sha1": "adc4c9b5fb14b6089caf57ee3ad6f6d48d11234b",
          "md5": "64820d88bb87fa51c3519fa20590e164"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle71",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle74JavadocElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "javadoc",
        "org.gradle.plugin.api-version": "7.4",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle74-javadoc.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle74-javadoc.jar",
          "size": 929727,
          "sha512": "1500adbdfcb1020453ea3b01e28819186adf7525de22100dc2f70b7f324d026d05edd67f9ddbdec705885f5980f307f0293168adc536f6ff68904c77e963821c",
          "sha256": "a43ecb08263265562e79982b2fc2540e544935452302af8e29c18e324713b206",
          "sha1": "28a2d2e9bb2e1a284504f609701358da68921a4c",
          "md5": "1e5c4f4bcb54f7cdff282de8e7dc2e1f"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle74",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle74SourcesElements",
      "attributes": {
        "org.gradle.category": "documentation",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.docstype": "sources",
        "org.gradle.plugin.api-version": "7.4",
        "org.gradle.usage": "java-runtime"
      },
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle74-sources.jar",
          "url": "kotlin-gradle-plugin-api-1.9.23-gradle74-sources.jar",
          "size": 58933,
          "sha512": "e63963303f9a89eb73330360d269f3f60e7806a535efa935d05993f3054ad212aef8edc72776a234985b836e83d023845d9e100865eed8d465647e1bce6978eb",
          "sha256": "d4dd401142bb732699d42ce9bb8dac88b4e548c0c9563a0e120a74b409331563",
          "sha1": "7f145d52cd9f88cd75eab4fc2afd57741436d67c",
          "md5": "7021853e5c977f0c971918f7d637454e"
        }
      ],
      "capabilities": [
        {
          "group": "org.jetbrains.kotlin",
          "name": "kotlin-gradle-plugin-api-gradle74",
          "version": "1.9.23"
        }
      ]
    },
    {
      "name": "gradle74ApiElements",
      "attributes": {
        "org.gradle.category": "library",
        "org.gradle.dependency.bundling": "external",
        "org.gradle.jvm.environment": "standard-jvm",
        "org.gradle.jvm.version": 8,
        "org.gradle.libraryelements": "jar",
        "org.gradle.plugin.api-version": "7.4",
        "org.gradle.usage": "java-api"
      },
      "dependencies": [
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugins-bom",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ],
          "attributes": {
            "org.gradle.category": "platform"
          },
          "endorseStrictVersions": true
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-gradle-plugin-annotations",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-native-utils",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-project-model",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        },
        {
          "group": "org.jetbrains.kotlin",
          "module": "kotlin-tooling-core",
          "version": {
            "requires": "1.9.23"
          },
          "excludes": [
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-reflect"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk8"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-jdk7"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-script-runtime"
            },
            {
              "group": "org.jetbrains.kotlin",
              "module": "kotlin-stdlib-common"
            }
          ]
        }
      ],
      "files": [
        {
          "name": "kotlin-gradle-plugin-api-1.9.23-gradle74.jar",
          "url": "kot