const express = require('express');
const router = express.Router();
const readingController = require('../controllers/reading.controller');
const { authJwt } = require('../middleware');

// 安全路由包装器
function safeRoute(controller, methodName) {
  return async (req, res) => {
    try {
      if (typeof controller[methodName] === 'function') {
        await controller[methodName](req, res);
      } else {
        console.error(`控制器方法不存在: ${methodName}`);
        res.status(500).send({ 
          message: `功能暂未实现: ${methodName}`,
          error: "NOT_IMPLEMENTED" 
        });
      }
    } catch (error) {
      console.error(`路由错误 [${methodName}]:`, error);
      res.status(500).send({ 
        message: "服务器内部错误",
        error: error.message 
      });
    }
  };
}

// 确保所有必需的控制器函数都存在
function ensureControllerFunctions() {
  const requiredFunctions = [
    "createReading", 
    "getAllReadings", 
    "getMyReadings",
    "getPendingReadings", 
    "getReadingDetail", 
    "confirmReading", 
    "addComment"
  ];
  
  requiredFunctions.forEach(funcName => {
    if (typeof readingController[funcName] !== 'function') {
      console.warn(`警告: 控制器缺少${funcName}函数，添加空实现`);
      
      readingController[funcName] = function(req, res) {
        res.status(500).send({ 
          message: `功能暂未实现: ${funcName}`,
          error: "NOT_IMPLEMENTED" 
        });
      };
    }
  });
}

// 确保所有必需的控制器函数都存在
ensureControllerFunctions();

// 所有路由都需要验证token
router.use(authJwt.verifyToken);

// 创建新的阅读任务
router.post("/", safeRoute(readingController, "createReading"));

// 获取所有阅读任务
router.get("/", safeRoute(readingController, "getAllReadings"));

// 获取我发起的阅读
router.get("/my", safeRoute(readingController, "getMyReadings"));

// 获取待我阅读的任务
router.get("/pending", safeRoute(readingController, "getPendingReadings"));

// 获取阅读详情
router.get("/:id", safeRoute(readingController, "getReadingDetail"));

// 确认已读
router.post("/:id/confirm", safeRoute(readingController, "confirmReading"));

// 添加阅读意见
router.post("/:id/comment", safeRoute(readingController, "addComment"));

module.exports = router;
