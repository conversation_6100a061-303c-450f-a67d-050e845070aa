package com.oaapproval.system.ui.screens.reading

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.oaapproval.system.data.model.*
import com.oaapproval.system.viewmodel.ReadingViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReadingDetailScreen(
    navController: NavController,
    readingId: String?,
    readingViewModel: ReadingViewModel = viewModel()
) {
    var showCommentDialog by remember { mutableStateOf(false) }
    var commentText by remember { mutableStateOf("") }
    
    val reading = readingViewModel.currentReading
    
    // 加载阅读详情
    LaunchedEffect(readingId) {
        readingId?.let {
            readingViewModel.loadReadingDetail(it)
        }
    }
    
    // 清理当前阅读详情
    DisposableEffect(Unit) {
        onDispose {
            readingViewModel.clearCurrentReading()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("阅读详情") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        if (readingViewModel.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (reading == null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.Error,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "阅读任务不存在",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 阅读基本信息
                item {
                    ReadingInfoCard(reading = reading)
                }
                
                // 阅读对象列表
                item {
                    ReadingStepsCard(
                        steps = reading.steps,
                        currentUserId = "", // TODO: 从AuthViewModel获取当前用户ID
                        onConfirmReading = { comment ->
                            readingId?.let { id ->
                                readingViewModel.confirmReading(
                                    readingId = id,
                                    comment = comment,
                                    onSuccess = {
                                        // 确认成功
                                    },
                                    onError = { error ->
                                        // 错误处理
                                    }
                                )
                            }
                        }
                    )
                }
                
                // 阅读意见列表
                if (reading.comments.isNotEmpty()) {
                    item {
                        ReadingCommentsCard(comments = reading.comments)
                    }
                }
                
                // 添加意见按钮
                item {
                    Button(
                        onClick = { showCommentDialog = true },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Comment, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("添加意见")
                    }
                }
            }
        }
        
        // 错误信息显示
        readingViewModel.errorMessage?.let { error ->
            LaunchedEffect(error) {
                // 可以显示Snackbar或其他错误提示
            }
        }
    }
    
    // 添加意见对话框
    if (showCommentDialog) {
        AlertDialog(
            onDismissRequest = { showCommentDialog = false },
            title = { Text("添加阅读意见") },
            text = {
                OutlinedTextField(
                    value = commentText,
                    onValueChange = { commentText = it },
                    label = { Text("请输入您的意见") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    maxLines = 5
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        if (commentText.isNotBlank()) {
                            readingId?.let { id ->
                                readingViewModel.addReadingComment(
                                    readingId = id,
                                    content = commentText.trim(),
                                    onSuccess = {
                                        commentText = ""
                                        showCommentDialog = false
                                    },
                                    onError = { error ->
                                        // 错误处理
                                    }
                                )
                            }
                        }
                    },
                    enabled = commentText.isNotBlank()
                ) {
                    Text("提交")
                }
            },
            dismissButton = {
                TextButton(onClick = { showCommentDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun ReadingInfoCard(reading: Reading) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Text(
                text = reading.title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 基本信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    InfoItem(label = "发起人", value = reading.creator.realName)
                    InfoItem(label = "发起时间", value = formatDateTime(reading.submitTime))
                }
                Column(horizontalAlignment = Alignment.End) {
                    ReadingPriorityChip(priority = reading.priority)
                    Spacer(modifier = Modifier.height(8.dp))
                    ReadingStatusChip(status = reading.status)
                }
            }
            
            // 描述
            if (!reading.description.isNullOrBlank()) {
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = "阅读内容：",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = reading.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun ReadingStepsCard(
    steps: List<ReadingStep>,
    currentUserId: String,
    onConfirmReading: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "阅读对象 (${steps.size}人)",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            steps.forEach { step ->
                ReadingStepItem(
                    step = step,
                    isCurrentUser = step.readerId == currentUserId,
                    onConfirmReading = onConfirmReading
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun ReadingStepItem(
    step: ReadingStep,
    isCurrentUser: Boolean,
    onConfirmReading: (String) -> Unit
) {
    var showConfirmDialog by remember { mutableStateOf(false) }
    var confirmComment by remember { mutableStateOf("") }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = step.reader.realName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = step.reader.position ?: "",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            if (step.readTime != null) {
                Text(
                    text = "阅读时间：${formatDateTime(step.readTime)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 状态标签
            ReadingStepStatusChip(status = step.status)
            
            // 确认已读按钮（仅当前用户且未读时显示）
            if (isCurrentUser && step.status == ReadingStepStatus.UNREAD) {
                Spacer(modifier = Modifier.width(8.dp))
                Button(
                    onClick = { showConfirmDialog = true },
                    modifier = Modifier.height(32.dp)
                ) {
                    Text("确认已读", fontSize = 12.sp)
                }
            }
        }
    }
    
    // 确认已读对话框
    if (showConfirmDialog) {
        AlertDialog(
            onDismissRequest = { showConfirmDialog = false },
            title = { Text("确认已读") },
            text = {
                Column {
                    Text("确认您已阅读此内容？")
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = confirmComment,
                        onValueChange = { confirmComment = it },
                        label = { Text("意见（可选）") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        onConfirmReading(confirmComment)
                        showConfirmDialog = false
                        confirmComment = ""
                    }
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(onClick = { showConfirmDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun ReadingCommentsCard(comments: List<ReadingComment>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "阅读意见 (${comments.size}条)",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            comments.forEach { comment ->
                ReadingCommentItem(comment = comment)
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
private fun ReadingCommentItem(comment: ReadingComment) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = comment.commenter.realName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = formatDateTime(comment.commentTime),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = comment.content,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

// 复用之前定义的组件
@Composable
private fun InfoItem(label: String, value: String) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun ReadingStepStatusChip(status: ReadingStepStatus) {
    val (text, color) = when (status) {
        ReadingStepStatus.UNREAD -> "未读" to Color(0xFFF59E0B)
        ReadingStepStatus.READ -> "已读" to Color(0xFF10B981)
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(color.copy(alpha = 0.1f))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontSize = 10.sp
        )
    }
}

@Composable
private fun ReadingStatusChip(status: ReadingStatus) {
    val (text, color) = when (status) {
        ReadingStatus.PENDING -> "进行中" to Color(0xFF3B82F6)
        ReadingStatus.COMPLETED -> "已完成" to Color(0xFF10B981)
    }

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(color.copy(alpha = 0.1f))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontSize = 10.sp
        )
    }
}

@Composable
private fun ReadingPriorityChip(priority: ReadingPriority) {
    val color = when (priority) {
        ReadingPriority.NORMAL -> Color(0xFF6B7280)
        ReadingPriority.IMPORTANT -> Color(0xFFF59E0B)
        ReadingPriority.URGENT -> Color(0xFFEF4444)
    }

    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(8.dp))
            .background(color.copy(alpha = 0.1f))
            .padding(horizontal = 6.dp, vertical = 2.dp)
    ) {
        Text(
            text = priority.displayName,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            fontSize = 9.sp
        )
    }
}

private fun formatDateTime(dateTimeString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val outputFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
        val date = inputFormat.parse(dateTimeString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateTimeString
    }
}
