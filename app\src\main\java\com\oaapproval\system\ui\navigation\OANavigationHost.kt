package com.oaapproval.system.ui.navigation

import android.net.Uri

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import androidx.navigation.NavType
import androidx.navigation.NavDestination
import com.oaapproval.system.ui.screens.approval.ApprovalDetailScreen
import com.oaapproval.system.ui.screens.approval.ApprovalHistoryScreen
import com.oaapproval.system.ui.screens.approval.CreateApprovalScreen
import com.oaapproval.system.ui.screens.approval.MyApprovalsScreen
import com.oaapproval.system.ui.screens.approval.PendingApprovalsScreen
import com.oaapproval.system.ui.screens.approval.SelectApproversScreen
import com.oaapproval.system.ui.screens.reading.CreateReadingScreen
import com.oaapproval.system.ui.screens.reading.ReadingDetailScreen

import com.oaapproval.system.ui.screens.auth.LoginScreen
import com.oaapproval.system.ui.screens.auth.RegisterScreen
import com.oaapproval.system.ui.screens.home.EmployeeHomeScreen


import com.oaapproval.system.ui.screens.profile.ChangePasswordScreen
import com.oaapproval.system.ui.screens.profile.EditProfileScreen
import com.oaapproval.system.ui.screens.profile.ProfileScreen

import com.oaapproval.system.ui.components.MainContainer
import com.oaapproval.system.viewmodel.ApprovalHistoryViewModel
import com.oaapproval.system.viewmodel.AuthViewModel
import com.oaapproval.system.MainActivity
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.viewmodel.compose.viewModel

// 微信风格的无痕切换 - 移除所有动画配置

@Composable
fun OANavigationHost(
    navController: NavHostController = rememberNavController(),
    externalFileUri: Uri? = null
) {
    // 添加导航监听器
    val navigationListener = NavController.OnDestinationChangedListener { 
        controller: NavController, 
        destination: NavDestination, 
        arguments: Bundle? ->
        Log.d("Navigation", "导航到: ${destination.route}, 参数: $arguments")
    }
    
    // 添加监听器
    navController.addOnDestinationChangedListener(navigationListener)
    
    // 创建视图模型实例
    val approvalHistoryViewModel: ApprovalHistoryViewModel = viewModel()
    val authViewModel: AuthViewModel = viewModel()

    // 获取当前用户状态
    val currentUser by authViewModel.currentUser.collectAsState()

    // 获取外部文件URI
    val externalFileUri = MainActivity.externalFileUri

    // 检查是否有待处理的通知导航
    val hasPendingNotification = MainActivity.pendingApprovalId != null

    // 根据登录状态决定起始路由
    val startDestination = currentUser?.let { user ->
        Log.d("Navigation", "用户已登录: ${user.realName}, 导航到首页")
        Screen.EmployeeHome.route
    } ?: run {
        // 检查是否有token但用户信息还没加载
        val token = com.oaapproval.system.data.api.ApiClient.getToken()
        if (!token.isNullOrEmpty() && hasPendingNotification) {
            Log.d("Navigation", "有token且有待处理通知，等待用户信息加载...")
            // 有token说明用户已登录，只是信息还没加载完，先导航到首页
            Screen.EmployeeHome.route
        } else {
            Log.d("Navigation", "用户未登录，导航到登录页")
            Screen.Login.route
        }
    }

    // 处理外部文件 - 简化逻辑，直接跳转
    LaunchedEffect(externalFileUri) {
        if (externalFileUri != null) {
            Log.d("Navigation", "检测到外部文件: $externalFileUri")
            // 等待一下让UI准备好，然后直接跳转
            kotlinx.coroutines.delay(1000)
            Log.d("Navigation", "跳转到选择审批人页面")
            navController.navigate(Screen.SelectApprovers.route) {
                // 清除登录页面，避免用户按返回键回到登录页
                popUpTo(Screen.Login.route) { inclusive = true }
            }
        }
    }

    // 处理通知点击导航
    LaunchedEffect(currentUser) {
        // 当用户登录后，检查是否有待处理的通知导航
        if (currentUser != null) {
            val pendingApprovalId = MainActivity.consumePendingApprovalId()
            if (!pendingApprovalId.isNullOrEmpty()) {
                Log.d("Navigation", "用户已登录，处理通知点击导航到审批: $pendingApprovalId")

                // 等待一下确保UI准备好
                kotlinx.coroutines.delay(500)

                // 导航到审批详情页面
                try {
                    navController.navigate("${Screen.ApprovalDetail.route}?approvalId=$pendingApprovalId") {
                        // 不清除登录页面，而是确保从首页导航
                        popUpTo(Screen.EmployeeHome.route) { inclusive = false }
                    }
                    Log.d("Navigation", "通知点击导航成功")
                } catch (e: Exception) {
                    Log.e("Navigation", "通知点击导航失败", e)
                }
            }
        }
    }

    MainContainer(navController = navController) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = startDestination,
            modifier = Modifier.padding(paddingValues)
        ) {
        // 认证相关
        composable(
            route = Screen.Login.route + "?username={username}&password={password}",
            arguments = listOf(
                navArgument("username") { 
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
                navArgument("password") { 
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            ),

        ) { backStackEntry ->
            val username = backStackEntry.arguments?.getString("username")
            val password = backStackEntry.arguments?.getString("password")
            Log.d("Navigation", "加载登录页面，用户名: $username")
            LoginScreen(
                navController = navController,
                registeredUserName = username,
                registeredPassword = password
            )
        }
        
        composable(route = Screen.Login.route) {
            Log.d("Navigation", "加载登录页面")
            LoginScreen(navController = navController)
        }
        
        composable(route = Screen.Register.route) {
            Log.d("Navigation", "加载注册页面")
            RegisterScreen(navController = navController)
        }
        
        // 主页 - 统一使用EmployeeHomeScreen
        composable(route = Screen.EmployeeHome.route) {
            Log.d("Navigation", "加载员工首页")
            EmployeeHomeScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }

        composable(route = Screen.Home.route) {
            Log.d("Navigation", "加载通用首页")
            EmployeeHomeScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }
        
        // 审批相关
        composable(route = Screen.SelectLeader.route) {
            Log.d("Navigation", "加载选择领导页面 - 重定向到选择审批人")
            SelectApproversScreen(navController = navController)
        }
        
        // 新增的多选审批人界面
        composable(
            route = Screen.SelectApprovers.route + "?mode={mode}",
            arguments = listOf(
                navArgument("mode") {
                    type = NavType.StringType
                    defaultValue = "multi"
                }
            )
        ) { backStackEntry ->
            val mode = backStackEntry.arguments?.getString("mode") ?: "multi"
            val title = when (mode) {
                "reading" -> "选择阅读对象"
                "single" -> "选择对象"
                else -> "选择审批人"
            }
            Log.d("Navigation", "加载选择页面，模式: $mode")
            SelectApproversScreen(
                navController = navController,
                mode = mode,
                title = title
            )
        }

        composable(route = Screen.SelectApprovers.route) {
            Log.d("Navigation", "加载选择审批人页面")
            SelectApproversScreen(navController = navController)
        }
        
        // 选择流转对象界面（复用选择审批人界面）
        composable(
            route = "select_user_for_transfer?excludeUserId={excludeUserId}",
            arguments = listOf(navArgument("excludeUserId") {
                type = NavType.StringType
                defaultValue = ""
            })
        ) { backStackEntry ->
            val excludeUserId = backStackEntry.arguments?.getString("excludeUserId") ?: ""
            Log.d("Navigation", "加载选择流转对象页面，排除用户ID: $excludeUserId")
            SelectApproversScreen(
                navController = navController,
                mode = "single",
                title = "选择流转对象",
                excludeUserId = excludeUserId,
                onUserSelected = { userId ->
                    Log.d("Navigation", "选择了流转对象: $userId")
                    // 在审批详情页面中设置全局变量
                    navController.previousBackStackEntry?.savedStateHandle?.set("selected_transfer_user_id", userId)
                }
            )
        }
        
        composable(
            route = Screen.CreateApproval.route + "?approverId={approverId}&externalFileUri={externalFileUri}",
            arguments = listOf(
                navArgument("approverId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                },
                navArgument("externalFileUri") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val approverId = backStackEntry.arguments?.getString("approverId")
            val externalFileUriString = backStackEntry.arguments?.getString("externalFileUri")
            val externalFileUri = externalFileUriString?.let { Uri.parse(it) }
            Log.d("Navigation", "加载创建审批页面，审批人ID: $approverId, 外部文件: $externalFileUri")
            CreateApprovalScreen(
                navController = navController,
                selectedApproverId = approverId,
                externalFileUri = externalFileUri
            )
        }

        // 发起阅读页面
        composable(route = Screen.CreateReading.route) {
            Log.d("Navigation", "加载发起阅读页面")
            CreateReadingScreen(navController = navController)
        }

        // 阅读详情页面
        composable(
            route = Screen.ReadingDetail.route + "?readingId={readingId}",
            arguments = listOf(
                navArgument("readingId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val readingId = backStackEntry.arguments?.getString("readingId")
            Log.d("Navigation", "加载阅读详情页面，阅读ID: $readingId")
            ReadingDetailScreen(
                navController = navController,
                readingId = readingId
            )
        }

        composable(
            route = Screen.ApprovalDetail.route + "?approvalId={approvalId}",
            arguments = listOf(
                navArgument("approvalId") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val approvalId = backStackEntry.arguments?.getString("approvalId")
            Log.d("Navigation", "加载审批详情页面，审批ID: $approvalId")
            ApprovalDetailScreen(
                navController = navController,
                approvalId = approvalId
            )
        }
        
        composable(route = Screen.ApprovalDetail.route) {
            Log.d("Navigation", "加载审批详情页面（无ID）")
            ApprovalDetailScreen(navController = navController)
        }
        
        composable(route = Screen.ApprovalHistory.route) {
            Log.d("Navigation", "加载审批历史页面")
            ApprovalHistoryScreen(
                navController = navController,
                viewModel = approvalHistoryViewModel
            )
        }
        
        // 添加我的申请和待我审批的路由
        composable(route = Screen.MyApprovals.route) {
            Log.d("Navigation", "加载我的申请页面")
            MyApprovalsScreen(navController = navController)
        }
        
        composable(route = Screen.PendingApprovals.route) {
            Log.d("Navigation", "加载待我审批页面")
            PendingApprovalsScreen(navController = navController)
        }
        
        // 个人中心
        composable(route = Screen.Profile.route) {
            Log.d("Navigation", "加载个人中心页面")
            ProfileScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }
        
        composable(route = Screen.ChangePassword.route) {
            Log.d("Navigation", "加载修改密码页面")
            ChangePasswordScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }

        composable(route = Screen.EditProfile.route) {
            Log.d("Navigation", "加载编辑个人资料页面")
            EditProfileScreen(
                navController = navController,
                authViewModel = authViewModel
            )
        }



        }
    }
}

sealed class Screen(val route: String) {
    object Login : Screen("login")
    object Register : Screen("register")
    object EmployeeHome : Screen("employee_home")
    object Home : Screen("home") // 通用首页路由
    object SelectLeader : Screen("select_leader")
    object SelectApprovers : Screen("select_approvers") // 新增多选审批人路由
    object CreateApproval : Screen("create_approval")
    object CreateReading : Screen("create_reading") // 新增发起阅读路由
    object ReadingDetail : Screen("reading_detail") // 新增阅读详情路由
    object ApprovalDetail : Screen("approval_detail")
    object ApprovalHistory : Screen("approval_history")
    object MyApprovals : Screen("my_approvals")
    object PendingApprovals : Screen("pending_approvals")
    object SelectTransferUser : Screen("select_user_for_transfer") // 新增选择流转对象路由

    object Profile : Screen("profile")
    object ChangePassword : Screen("change_password")
    object EditProfile : Screen("edit_profile")

}