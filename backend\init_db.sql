-- 创建数据库
CREATE DATABASE IF NOT EXISTS oa_mysql CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE oa_mysql;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  real_name VARCHAR(100) NOT NULL,
  position VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL
);

-- 创建审批申请表
CREATE TABLE IF NOT EXISTS approvals (
  id VARCHAR(36) PRIMARY KEY,
  request_number VARCHAR(50) NOT NULL UNIQUE,
  title VARCHAR(200) NOT NULL,
  status ENUM('PENDING', 'APPROVED', 'REJECTED', 'TRANSFERRED') DEFAULT 'PENDING',
  priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL',
  applicant_id VARCHAR(36) NOT NULL,
  current_step INT DEFAULT 0,
  submit_time DATETIME NOT NULL,
  description TEXT,
  completed_at DATETIME NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (applicant_id) REFERENCES users(id)
);

-- 创建审批步骤表
CREATE TABLE IF NOT EXISTS approval_steps (
  id VARCHAR(36) PRIMARY KEY,
  approval_id VARCHAR(36) NOT NULL,
  step_name VARCHAR(100) NOT NULL,
  step_order INT NOT NULL DEFAULT 1,
  approver_id VARCHAR(36) NOT NULL,
  status ENUM('COMPLETED', 'CURRENT', 'PENDING', 'REJECTED') DEFAULT 'PENDING',
  process_time DATETIME,
  comment TEXT,
  estimated_process_time VARCHAR(100),
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (approval_id) REFERENCES approvals(id),
  FOREIGN KEY (approver_id) REFERENCES users(id)
);

-- 创建审批意见表
CREATE TABLE IF NOT EXISTS approval_comments (
  id VARCHAR(36) PRIMARY KEY,
  approval_id VARCHAR(36) NOT NULL,
  commenter_id VARCHAR(36) NOT NULL,
  content TEXT NOT NULL,
  comment_time DATETIME NOT NULL,
  comment_type ENUM('APPROVAL', 'REJECTION', 'TRANSFER', 'COMMENT_ONLY') DEFAULT 'COMMENT_ONLY',
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (approval_id) REFERENCES approvals(id),
  FOREIGN KEY (commenter_id) REFERENCES users(id)
);

-- 创建附件表
CREATE TABLE IF NOT EXISTS attachments (
  id VARCHAR(36) PRIMARY KEY,
  approval_id VARCHAR(36) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_size VARCHAR(50) NOT NULL,
  file_type VARCHAR(100) NOT NULL,
  upload_time DATETIME NOT NULL,
  download_url VARCHAR(255),
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (approval_id) REFERENCES approvals(id)
);

-- 创建流转记录表
CREATE TABLE IF NOT EXISTS transfer_records (
  id VARCHAR(36) PRIMARY KEY,
  approval_id VARCHAR(36) NOT NULL,
  from_user_id VARCHAR(36) NOT NULL,
  to_user_id VARCHAR(36) NOT NULL,
  transfer_time DATETIME NOT NULL,
  comment TEXT,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (approval_id) REFERENCES approvals(id),
  FOREIGN KEY (from_user_id) REFERENCES users(id),
  FOREIGN KEY (to_user_id) REFERENCES users(id)
);

-- 创建阅读任务表
CREATE TABLE IF NOT EXISTS readings (
  id VARCHAR(36) PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  priority ENUM('NORMAL', 'IMPORTANT', 'URGENT') DEFAULT 'NORMAL',
  creator_id VARCHAR(36) NOT NULL,
  status ENUM('PENDING', 'COMPLETED') DEFAULT 'PENDING',
  submit_time DATETIME NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (creator_id) REFERENCES users(id)
);

-- 创建阅读步骤表
CREATE TABLE IF NOT EXISTS reading_steps (
  id VARCHAR(36) PRIMARY KEY,
  reading_id VARCHAR(36) NOT NULL,
  reader_id VARCHAR(36) NOT NULL,
  status ENUM('UNREAD', 'READ') DEFAULT 'UNREAD',
  read_time DATETIME,
  comment TEXT,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (reading_id) REFERENCES readings(id),
  FOREIGN KEY (reader_id) REFERENCES users(id)
);

-- 创建阅读附件表
CREATE TABLE IF NOT EXISTS reading_attachments (
  id VARCHAR(36) PRIMARY KEY,
  reading_id VARCHAR(36) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT,
  file_type VARCHAR(100),
  upload_time DATETIME NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (reading_id) REFERENCES readings(id)
);

-- 创建阅读意见表
CREATE TABLE IF NOT EXISTS reading_comments (
  id VARCHAR(36) PRIMARY KEY,
  reading_id VARCHAR(36) NOT NULL,
  commenter_id VARCHAR(36) NOT NULL,
  content TEXT NOT NULL,
  comment_time DATETIME NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,
  FOREIGN KEY (reading_id) REFERENCES readings(id),
  FOREIGN KEY (commenter_id) REFERENCES users(id)
);

-- 创建初始管理员用户
INSERT INTO users (id, username, password, real_name, position, created_at, updated_at)
VALUES (
    'admin-uuid-1234567890',
    'admin',
    '$2a$08$TQETsi01Zxo7r6ORnZ1kFOLHyVgIxYxGJVNQc2FbVgkFWggYKh9vy', -- 密码: admin123
    '系统管理员',
    '管理员',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE username = username;