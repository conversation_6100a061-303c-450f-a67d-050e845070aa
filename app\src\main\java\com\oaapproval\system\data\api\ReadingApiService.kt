package com.oaapproval.system.data.api

import com.oaapproval.system.data.model.*
import retrofit2.Response
import retrofit2.http.*

interface ReadingApiService {
    
    // 创建阅读任务
    @POST("reading/")
    suspend fun createReading(@Body request: CreateReadingRequest): Response<CreateReadingResponse>
    
    // 获取所有阅读任务
    @GET("reading/")
    suspend fun getAllReadings(): Response<List<Reading>>
    
    // 获取我发起的阅读
    @GET("reading/my")
    suspend fun getMyReadings(): Response<List<Reading>>
    
    // 获取待我阅读的任务
    @GET("reading/pending")
    suspend fun getPendingReadings(): Response<List<Reading>>
    
    // 获取阅读详情
    @GET("reading/{id}")
    suspend fun getReadingDetail(@Path("id") readingId: String): Response<Reading>
    
    // 确认已读
    @POST("reading/{id}/confirm")
    suspend fun confirmReading(
        @Path("id") readingId: String,
        @Body request: ConfirmReadingRequest
    ): Response<MessageResponse>

    // 添加阅读意见
    @POST("reading/{id}/comment")
    suspend fun addReadingComment(
        @Path("id") readingId: String,
        @Body request: AddReadingCommentRequest
    ): Response<MessageResponse>
}
