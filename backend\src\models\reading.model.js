module.exports = (sequelize, Sequelize) => {
  const Reading = sequelize.define("reading", {
    id: {
      type: Sequelize.STRING(36),
      primaryKey: true,
      defaultValue: Sequelize.UUIDV4
    },
    title: {
      type: Sequelize.STRING(200),
      allowNull: false
    },
    description: {
      type: Sequelize.TEXT,
      allowNull: true
    },
    priority: {
      type: Sequelize.ENUM('NORMAL', 'IMPORTANT', 'URGENT'),
      defaultValue: 'NORMAL',
      allowNull: false
    },
    creatorId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'creator_id'
    },
    status: {
      type: Sequelize.ENUM('PENDING', 'COMPLETED'),
      defaultValue: 'PENDING',
      allowNull: false
    },
    submitTime: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'submit_time'
    }
  }, {
    tableName: 'readings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Reading;
};
