package com.oaapproval.system.viewmodel

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class UserViewModel : ViewModel() {
    
    // 选择的用户ID列表
    private val _selectedUserIds = MutableStateFlow<List<String>>(emptyList())
    val selectedUserIds: StateFlow<List<String>> = _selectedUserIds.asStateFlow()
    
    // 设置选择的用户ID列表
    fun setSelectedUserIds(userIds: List<String>) {
        _selectedUserIds.value = userIds
    }
    
    // 添加用户ID
    fun addSelectedUserId(userId: String) {
        val currentList = _selectedUserIds.value.toMutableList()
        if (!currentList.contains(userId)) {
            currentList.add(userId)
            _selectedUserIds.value = currentList
        }
    }
    
    // 移除用户ID
    fun removeSelectedUserId(userId: String) {
        val currentList = _selectedUserIds.value.toMutableList()
        currentList.remove(userId)
        _selectedUserIds.value = currentList
    }
    
    // 清空选择的用户
    fun clearSelectedUsers() {
        _selectedUserIds.value = emptyList()
    }
    
    // 检查用户是否已选择
    fun isUserSelected(userId: String): Boolean {
        return _selectedUserIds.value.contains(userId)
    }
}
