module.exports = (sequelize, Sequelize) => {
  const ReadingAttachment = sequelize.define("reading_attachment", {
    id: {
      type: Sequelize.STRING(36),
      primaryKey: true,
      defaultValue: Sequelize.UUIDV4
    },
    readingId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'reading_id'
    },
    fileName: {
      type: Sequelize.STRING(255),
      allowNull: false,
      field: 'file_name'
    },
    filePath: {
      type: Sequelize.STRING(500),
      allowNull: false,
      field: 'file_path'
    },
    fileSize: {
      type: Sequelize.BIGINT,
      allowNull: true,
      field: 'file_size'
    },
    fileType: {
      type: Sequelize.STRING(100),
      allowNull: true,
      field: 'file_type'
    },
    uploadTime: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'upload_time'
    }
  }, {
    tableName: 'reading_attachments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return ReadingAttachment;
};
