package com.oaapproval.system.data.api

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.GsonBuilder
import com.oaapproval.system.config.ApiConfig
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.ResponseBody
import okio.buffer
import okio.source
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.nio.charset.Charset
import java.util.concurrent.TimeUnit

object ApiClient {
    private const val TAG = "ApiClient"
    private const val PREFS_NAME = "oa_api_prefs"
    private const val KEY_TOKEN = "auth_token"

    private var initialized = false
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var currentContext: Context

    fun init(context: Context) {
        currentContext = context.applicationContext
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        initialized = true
        val baseUrl = ApiConfig.getBaseUrl(context)
        Log.d(TAG, "ApiClient初始化完成，BASE_URL=$baseUrl")
        Log.d(TAG, "环境信息: ${ApiConfig.getEnvironmentInfo()}")
        
        // 检查是否已有token
        val token = getToken()
        if (!token.isNullOrEmpty()) {
            Log.d(TAG, "已存在token: ${token.take(10)}...")
        } else {
            Log.d(TAG, "未找到token")
        }
    }
    
    private fun checkInitialized() {
        if (!initialized) {
            Log.e(TAG, "ApiClient未初始化！请先调用init(context)方法")
            throw IllegalStateException("ApiClient未初始化，请先调用init(context)方法")
        }
    }
    
    // 保存token
    fun saveToken(token: String) {
        checkInitialized()
        Log.d(TAG, "保存token: ${token.take(10)}...")
        sharedPreferences.edit().putString(KEY_TOKEN, token).apply()
    }
    
    // 获取token
    fun getToken(): String? {
        if (!initialized) {
            Log.w(TAG, "ApiClient未初始化，无法获取token")
            return null
        }
        
        val token = sharedPreferences.getString(KEY_TOKEN, null)
        if (token != null) {
            Log.d(TAG, "获取token: ${token.take(10)}...")
        } else {
            Log.d(TAG, "获取token: null")
        }
        return token
    }
    
    // 清除token
    fun clearToken() {
        if (!initialized) {
            Log.w(TAG, "ApiClient未初始化，无法清除token")
            return
        }

        Log.d(TAG, "清除token")
        sharedPreferences.edit().remove(KEY_TOKEN).apply()
    }

    // 获取当前用户ID（从token中解析）
    fun getCurrentUserId(): String? {
        val token = getToken() ?: return null

        try {
            // JWT token格式：header.payload.signature
            val parts = token.split(".")
            if (parts.size != 3) {
                Log.w(TAG, "Token格式不正确")
                return null
            }

            // 解码payload部分
            val payload = parts[1]
            val decodedBytes = android.util.Base64.decode(payload, android.util.Base64.URL_SAFE)
            val payloadJson = String(decodedBytes)

            // 解析JSON获取用户ID
            val jsonObject = com.google.gson.JsonParser.parseString(payloadJson).asJsonObject
            val userId = jsonObject.get("id")?.asString

            Log.d(TAG, "从token解析用户ID: $userId")
            return userId
        } catch (e: Exception) {
            Log.e(TAG, "解析token失败", e)
            return null
        }
    }
    
    // 认证拦截器
    private class AuthInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val originalRequest = chain.request()
            
            // 如果没有token，直接发送原始请求
            val token = getToken() ?: return chain.proceed(originalRequest)
            
            // 添加token到请求头
            val newRequest = originalRequest.newBuilder()
                .header("Authorization", "Bearer $token")
                .build()
            
            Log.d(TAG, "请求添加Authorization头: ${originalRequest.url}")
            return chain.proceed(newRequest)
        }
    }
    
    // OkHttp客户端
    private val okHttpClient = OkHttpClient.Builder()
        .addInterceptor(PerformanceInterceptor())
        .addInterceptor(AuthInterceptor())
        .addInterceptor { chain ->
            // 添加日志拦截器，解决乱码问题和401错误处理
            val request = chain.request()
            val response = chain.proceed(request)

            // 如果是401错误，记录日志但不立即清除token
            // 让具体的业务逻辑决定如何处理401错误
            if (response.code == 401) {
                Log.w(TAG, "收到401错误 - URL: ${request.url}, 让业务逻辑处理")
                // 不再自动清除token，避免因单个请求失败导致整个应用登出
            }

            // 如果是错误响应，尝试修复乱码
            if (!response.isSuccessful) {
                val originalBody = response.body
                if (originalBody != null) {
                    val contentType = originalBody.contentType()
                    val source = originalBody.source()
                    source.request(Long.MAX_VALUE) // 缓存整个body
                    val buffer = source.buffer

                    // 尝试以UTF-8解码
                    val bodyString = buffer.clone().readString(Charset.forName("UTF-8"))
                    Log.d(TAG, "原始错误响应: $bodyString")

                    // 创建新的ResponseBody
                    val fixedBody = ResponseBody.create(contentType, bodyString)

                    // 记录日志，方便调试
                    if (bodyString.contains("message")) {
                        try {
                            val messageStart = bodyString.indexOf("message") + 10 // "message":"
                            val messageEnd = bodyString.indexOf("\"", messageStart)
                            if (messageStart > 0 && messageEnd > messageStart) {
                                val errorMessage = bodyString.substring(messageStart, messageEnd)
                                Log.e(TAG, "错误信息: $errorMessage")
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "解析错误信息失败", e)
                        }
                    }

                    return@addInterceptor response.newBuilder()
                        .body(fixedBody)
                        .build()
                }
            }

            response
        }
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    // 自定义Gson以处理日期格式
    private val gson = GsonBuilder()
        .setLenient() // 宽松模式，允许JSON格式不严格
        .serializeNulls() // 序列化null值
        .disableHtmlEscaping() // 禁用HTML转义，避免中文乱码
        .setDateFormat("yyyy-MM-dd HH:mm:ss") // 设置日期格式
        .create()
    
    // Retrofit实例 - 延迟初始化以获取动态BASE_URL
    private val retrofit by lazy {
        val baseUrl = if (::currentContext.isInitialized) {
            ApiConfig.getBaseUrl(currentContext)
        } else {
            "http://10.0.2.2:3000/api/" // 默认模拟器地址
        }
        Log.d(TAG, "创建Retrofit实例，BASE_URL=$baseUrl")
        Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    // API服务
    val apiService: ApiService by lazy { retrofit.create(ApiService::class.java) }
    val readingService: ReadingApiService by lazy { retrofit.create(ReadingApiService::class.java) }
} 