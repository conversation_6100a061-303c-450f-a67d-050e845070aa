module.exports = (sequelize, Sequelize) => {
  const ReadingStep = sequelize.define("reading_step", {
    id: {
      type: Sequelize.STRING(36),
      primaryKey: true,
      defaultValue: Sequelize.UUIDV4
    },
    readingId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'reading_id'
    },
    readerId: {
      type: Sequelize.STRING(36),
      allowNull: false,
      field: 'reader_id'
    },
    status: {
      type: Sequelize.ENUM('UNREAD', 'READ'),
      defaultValue: 'UNREAD',
      allowNull: false
    },
    readTime: {
      type: Sequelize.DATE,
      allowNull: true,
      field: 'read_time'
    },
    comment: {
      type: Sequelize.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'reading_steps',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return ReadingStep;
};
