package com.oaapproval.system.ui.screens.reading

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.oaapproval.system.data.model.ReadingPriority
import com.oaapproval.system.ui.navigation.Screen
import com.oaapproval.system.viewmodel.ReadingViewModel
import com.oaapproval.system.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateReadingScreen(
    navController: NavController,
    readingViewModel: ReadingViewModel = viewModel(),
    userViewModel: UserViewModel = viewModel()
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var priority by remember { mutableStateOf(ReadingPriority.NORMAL) }
    var selectedReaderIds by remember { mutableStateOf<List<String>>(emptyList()) }
    var showPriorityMenu by remember { mutableStateOf(false) }
    
    // 监听选择的阅读对象
    LaunchedEffect(Unit) {
        userViewModel.selectedUserIds.collect { userIds ->
            selectedReaderIds = userIds
        }
    }
    
    // 监听创建结果
    LaunchedEffect(readingViewModel.errorMessage) {
        readingViewModel.errorMessage?.let { error ->
            // 这里可以显示错误提示
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("发起阅读") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 阅读标题
            OutlinedTextField(
                value = title,
                onValueChange = { title = it },
                label = { Text("阅读标题 *") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // 重要程度选择
            ExposedDropdownMenuBox(
                expanded = showPriorityMenu,
                onExpandedChange = { showPriorityMenu = !showPriorityMenu }
            ) {
                OutlinedTextField(
                    value = priority.displayName,
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("重要程度") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showPriorityMenu) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                ExposedDropdownMenu(
                    expanded = showPriorityMenu,
                    onDismissRequest = { showPriorityMenu = false }
                ) {
                    ReadingPriority.values().forEach { priorityOption ->
                        DropdownMenuItem(
                            text = { Text(priorityOption.displayName) },
                            onClick = {
                                priority = priorityOption
                                showPriorityMenu = false
                            }
                        )
                    }
                }
            }
            
            // 阅读对象显示
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "阅读对象 *",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    if (selectedReaderIds.isNotEmpty()) {
                        Text(
                            text = "已选择 ${selectedReaderIds.size} 人",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.primary
                        )
                    } else {
                        Text(
                            text = "未选择阅读对象",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
            }
            
            // 阅读内容
            OutlinedTextField(
                value = description,
                onValueChange = { description = it },
                label = { Text("阅读内容") },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp),
                maxLines = 5,
                placeholder = { Text("请输入阅读内容或说明...") }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 提交按钮
            Button(
                onClick = {
                    if (title.isBlank()) {
                        // 显示错误提示
                        return@Button
                    }
                    if (selectedReaderIds.isEmpty()) {
                        // 显示错误提示
                        return@Button
                    }
                    
                    readingViewModel.createReading(
                        title = title.trim(),
                        description = description.trim().takeIf { it.isNotEmpty() },
                        priority = priority.name,
                        readerIds = selectedReaderIds,
                        onSuccess = { readingId ->
                            // 清空选择的用户
                            userViewModel.clearSelectedUsers()
                            // 返回主页面
                            navController.navigate(Screen.EmployeeHome.route) {
                                popUpTo(Screen.EmployeeHome.route) { inclusive = true }
                            }
                        },
                        onError = { error ->
                            // 错误处理已在ViewModel中处理
                        }
                    )
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = !readingViewModel.isLoading && title.isNotBlank() && selectedReaderIds.isNotEmpty()
            ) {
                if (readingViewModel.isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text("发起阅读")
            }
            
            // 错误信息显示
            readingViewModel.errorMessage?.let { error ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = error,
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}
