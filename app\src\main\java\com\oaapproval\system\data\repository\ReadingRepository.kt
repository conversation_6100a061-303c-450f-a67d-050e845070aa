package com.oaapproval.system.data.repository

import android.util.Log
import com.oaapproval.system.data.api.ApiClient
import com.oaapproval.system.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ReadingRepository {
    private val readingService = ApiClient.readingService
    
    companion object {
        private const val TAG = "ReadingRepository"
    }
    
    // 创建阅读任务
    suspend fun createReading(
        title: String,
        description: String?,
        priority: String,
        readerIds: List<String>,
        attachments: List<ReadingAttachment>? = null
    ): Result<Reading> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "创建阅读任务: title=$title, readerIds=$readerIds")
            
            val request = CreateReadingRequest(
                title = title,
                description = description,
                priority = priority,
                readerIds = readerIds,
                attachments = attachments
            )
            
            val response = readingService.createReading(request)
            
            if (response.isSuccessful) {
                val createResponse = response.body()
                if (createResponse != null) {
                    Log.d(TAG, "创建阅读任务成功: ${createResponse.readingId}")
                    Result.success(createResponse.reading)
                } else {
                    Log.e(TAG, "创建阅读任务失败: 响应体为空")
                    Result.failure(Exception("创建阅读任务失败: 响应体为空"))
                }
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "创建阅读任务失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("创建阅读任务失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建阅读任务异常", e)
            Result.failure(e)
        }
    }
    
    // 获取所有阅读任务
    suspend fun getAllReadings(): Result<List<Reading>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取所有阅读任务")
            
            val response = readingService.getAllReadings()
            
            if (response.isSuccessful) {
                val readings = response.body() ?: emptyList()
                Log.d(TAG, "获取所有阅读任务成功: ${readings.size}个")
                Result.success(readings)
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "获取所有阅读任务失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("获取阅读任务失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取所有阅读任务异常", e)
            Result.failure(e)
        }
    }
    
    // 获取我发起的阅读
    suspend fun getMyReadings(): Result<List<Reading>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取我发起的阅读")
            
            val response = readingService.getMyReadings()
            
            if (response.isSuccessful) {
                val readings = response.body() ?: emptyList()
                Log.d(TAG, "获取我发起的阅读成功: ${readings.size}个")
                Result.success(readings)
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "获取我发起的阅读失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("获取我的阅读失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取我发起的阅读异常", e)
            Result.failure(e)
        }
    }
    
    // 获取待我阅读的任务
    suspend fun getPendingReadings(): Result<List<Reading>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取待我阅读的任务")
            
            val response = readingService.getPendingReadings()
            
            if (response.isSuccessful) {
                val readings = response.body() ?: emptyList()
                Log.d(TAG, "获取待我阅读的任务成功: ${readings.size}个")
                Result.success(readings)
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "获取待我阅读的任务失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("获取待阅读任务失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取待我阅读的任务异常", e)
            Result.failure(e)
        }
    }
    
    // 获取阅读详情
    suspend fun getReadingDetail(readingId: String): Result<Reading> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取阅读详情: $readingId")
            
            val response = readingService.getReadingDetail(readingId)
            
            if (response.isSuccessful) {
                val reading = response.body()
                if (reading != null) {
                    Log.d(TAG, "获取阅读详情成功: ${reading.title}")
                    Result.success(reading)
                } else {
                    Log.e(TAG, "获取阅读详情失败: 响应体为空")
                    Result.failure(Exception("获取阅读详情失败: 响应体为空"))
                }
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "获取阅读详情失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("获取阅读详情失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取阅读详情异常", e)
            Result.failure(e)
        }
    }
    
    // 确认已读
    suspend fun confirmReading(readingId: String, comment: String? = null): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "确认已读: $readingId")
            
            val request = ConfirmReadingRequest(comment = comment)
            val response = readingService.confirmReading(readingId, request)
            
            if (response.isSuccessful) {
                Log.d(TAG, "确认已读成功")
                Result.success(Unit)
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "确认已读失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("确认已读失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "确认已读异常", e)
            Result.failure(e)
        }
    }
    
    // 添加阅读意见
    suspend fun addReadingComment(readingId: String, content: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "添加阅读意见: $readingId")
            
            val request = AddReadingCommentRequest(content = content)
            val response = readingService.addReadingComment(readingId, request)
            
            if (response.isSuccessful) {
                Log.d(TAG, "添加阅读意见成功")
                Result.success(Unit)
            } else {
                val errorMsg = response.errorBody()?.string() ?: "未知错误"
                Log.e(TAG, "添加阅读意见失败: ${response.code()} - $errorMsg")
                Result.failure(Exception("添加阅读意见失败: $errorMsg"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "添加阅读意见异常", e)
            Result.failure(e)
        }
    }
}
