package com.oaapproval.system.ui.screens.approval

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Comment
import androidx.compose.material.icons.automirrored.filled.InsertDriveFile
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.ui.window.Dialog
import androidx.compose.runtime.*
import kotlinx.coroutines.withContext
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.activity.ComponentActivity
import android.util.Log
import coil.compose.AsyncImage
import com.oaapproval.system.data.model.*
import com.oaapproval.system.data.repository.UserRepository
import com.oaapproval.system.ui.components.MainScaffold
import com.oaapproval.system.ui.components.StatusBar
import com.oaapproval.system.ui.utils.SmartBottomSpacer
import com.oaapproval.system.ui.utils.SystemBarsUtils
import com.oaapproval.system.utils.DataCacheManager
import com.oaapproval.system.viewmodel.ApprovalViewModel
import com.oaapproval.system.viewmodel.AuthViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch



// 格式化提交时间（简化版本，后端已使用本地时间）
private fun formatSubmitTime(submitTime: String?): String {
    return submitTime?.takeIf { it.isNotBlank() && it != "未知时间" } ?: getCurrentTimeString()
}

// 获取当前时间字符串
private fun getCurrentTimeString(): String {
    return try {
        val formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        java.time.LocalDateTime.now().format(formatter)
    } catch (e: Exception) {
        "刚刚"
    }
}

// 格式化上传时间（简化版本，后端已使用本地时间）
private fun formatUploadTime(uploadTime: String?): String {
    return uploadTime?.takeIf { it.isNotBlank() && it != "未知时间" } ?: getCurrentTimeString()
}

// 格式化日期时间
private fun formatDateTime(dateTime: String?): String {
    return if (dateTime.isNullOrBlank()) {
        "未知时间"
    } else {
        dateTime
    }
}



// 构建完整的审批流程，包含流转历史
private fun buildApprovalSteps(approval: Approval): List<ApprovalStep> {
    val steps = mutableListOf<ApprovalStep>()

    // 优先使用后端返回的steps数据
    if (!approval.steps.isNullOrEmpty()) {
        android.util.Log.d("ApprovalFlow", "使用后端返回的审批步骤数据，共${approval.steps.size}个步骤")

        // 第1步：申请提交
        steps.add(
            ApprovalStep(
                id = "1",
                stepName = "申请提交",
                approver = approval.applicant,
                status = StepStatus.COMPLETED,
                comment = "申请已提交",
                processTime = approval.createTime
            )
        )

        // 添加后端返回的审批步骤 - 支持并行审批显示
        approval.steps.sortedBy { it.stepOrder }.forEachIndexed { index, backendStep ->
            val stepStatus = when (backendStep.status) {
                "COMPLETED" -> StepStatus.COMPLETED
                "REJECTED" -> StepStatus.REJECTED   // 拒绝状态单独处理
                "CURRENT" -> StepStatus.CURRENT     // 并行审批：可以审批的状态
                "PENDING" -> StepStatus.PENDING     // 等待状态（不再使用）
                "TRANSFERRED" -> StepStatus.TRANSFERRED // 已流转状态
                else -> StepStatus.PENDING
            }

            // 使用后端返回的实际审批意见，如果为空则使用状态描述
            val stepComment = if (!backendStep.comment.isNullOrBlank()) {
                backendStep.comment
            } else {
                when (backendStep.status) {
                    "COMPLETED" -> "${backendStep.approver.realName} 已通过"
                    "REJECTED" -> "${backendStep.approver.realName} 已拒绝"
                    "CURRENT" -> "${backendStep.approver.realName} 待审批"
                    else -> "${backendStep.approver.realName} 待处理"
                }
            }

            steps.add(
                ApprovalStep(
                    id = (index + 2).toString(), // 从第2步开始
                    stepName = backendStep.stepName,
                    approver = backendStep.approver,
                    status = stepStatus,
                    comment = stepComment,
                    processTime = backendStep.processTime
                )
            )
        }

        android.util.Log.d("ApprovalFlow", "构建的审批步骤: ${steps.map { "${it.approver.realName}(${it.status})" }}")
        return steps
    }

    // 如果没有后端steps数据，使用原来的逻辑（向后兼容）
    android.util.Log.d("ApprovalFlow", "后端未返回steps数据，使用流转历史构建审批步骤")

    // 添加详细的调试日志
    android.util.Log.d("ApprovalFlow", "=== 审批流程调试信息 ===")
    android.util.Log.d("ApprovalFlow", "申请ID: ${approval.id}")
    android.util.Log.d("ApprovalFlow", "申请人: ${approval.applicant?.realName}")
    android.util.Log.d("ApprovalFlow", "当前审批人: ${approval.approver?.realName}")
    android.util.Log.d("ApprovalFlow", "审批状态: ${approval.status}")
    android.util.Log.d("ApprovalFlow", "流转历史数量: ${approval.transferHistory?.size ?: 0}")
    approval.transferHistory?.forEachIndexed { index, transfer ->
        android.util.Log.d("ApprovalFlow", "流转[$index]: ${transfer.fromUser?.realName} -> ${transfer.toUser?.realName} (${transfer.transferTime})")
        android.util.Log.d("ApprovalFlow", "  备注: ${transfer.comment}")
    }

    // 分析审批链
    val approvalChain = mutableListOf<User>()

    if (approval.transferHistory?.isNotEmpty() == true) {
        // 有流转历史的情况
        // 第一个审批人是第一个流转记录的fromUser
        approval.transferHistory.firstOrNull()?.fromUser?.let { approvalChain.add(it) }

        // 中间的审批人是每个流转记录的toUser（除了最后一个）
        approval.transferHistory.forEach { transfer ->
            transfer.toUser?.let { approvalChain.add(it) }
        }
    } else {
        // 没有流转历史，只有一个审批人
        approval.approver?.let { approvalChain.add(it) }
    }

    android.util.Log.d("ApprovalFlow", "审批链: ${approvalChain.map { it.realName }.joinToString(" -> ")}")
    android.util.Log.d("ApprovalFlow", "最终审批人应该是: ${approvalChain.lastOrNull()?.realName}")
    android.util.Log.d("ApprovalFlow", "=== 调试信息结束 ===")

    // 第1步：申请提交
    steps.add(
        ApprovalStep(
            id = "1",
            stepName = "申请提交",
            approver = approvalChain.firstOrNull() ?: User(id = "0", realName = "未知审批人", position = "未知"),
            status = StepStatus.COMPLETED,
            processTime = approval.createTime,
            comment = "${approval.applicant?.realName ?: "未知用户"} 提交申请给 ${approvalChain.firstOrNull()?.realName ?: "未知审批人"}"
        )
    )

    // 第2步及后续：审批处理步骤
    // 基于审批链构建审批步骤
    approvalChain.forEachIndexed { index, approver ->
        val stepId = index + 2 // 从第2步开始
        val isLastApprover = index == approvalChain.size - 1

        // 确定步骤状态
        val stepStatus = when {
            // 如果不是最后一个审批人，状态为已完成
            !isLastApprover -> StepStatus.COMPLETED
            // 如果是最后一个审批人，根据整体审批状态确定
            approval.status == ApprovalStatus.PENDING -> StepStatus.CURRENT
            else -> StepStatus.COMPLETED
        }

        // 确定步骤备注
        val stepComment = when {
            !isLastApprover -> {
                // 不是最后一个审批人，显示流转信息
                val nextApprover = approvalChain.getOrNull(index + 1)
                val transferRecord = approval.transferHistory?.getOrNull(index)
                "${approver.realName} 将申请流转给 ${nextApprover?.realName ?: "下一审批人"}${
                    if (!transferRecord?.comment.isNullOrBlank()) "，备注：${transferRecord?.comment}" else ""
                }"
            }
            approval.status == ApprovalStatus.PENDING -> "${approver.realName} 正在审批中"
            approval.status == ApprovalStatus.APPROVED -> "${approver.realName} 审批通过"
            approval.status == ApprovalStatus.REJECTED -> "${approver.realName} 审批被拒绝"
            else -> "${approver.realName} 处理中"
        }

        // 确定处理时间
        val processTime = when {
            !isLastApprover -> approval.transferHistory?.getOrNull(index)?.transferTime
            approval.status != ApprovalStatus.PENDING -> approval.updateTime
            else -> null
        }

        steps.add(
            ApprovalStep(
                id = stepId.toString(),
                stepName = "审批处理",
                approver = approver,
                status = stepStatus,
                comment = stepComment,
                processTime = processTime
            )
        )
    }

    return steps
}

// 从流转历史和独立意见生成历史意见 - 只显示有备注的记录
private fun buildCommentsFromTransferHistory(approval: Approval): List<ApprovalComment> {
    val comments = mutableListOf<ApprovalComment>()
    
    // 添加独立的意见记录
    approval.comments?.forEach { comment ->
        // 确保commentTime不为null，如果为null则使用当前时间
        val safeCommentTime = comment.commentTime ?: java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())

        // 映射后端的commentType到前端枚举
        val mappedCommentType = when (comment.commentType) {
            "APPROVAL" -> CommentType.APPROVAL
            "REJECTION" -> CommentType.REJECTION
            "TRANSFER" -> CommentType.TRANSFER
            "COMMENT_ONLY" -> CommentType.COMMENT_ONLY
            null -> CommentType.COMMENT_ONLY
            else -> CommentType.COMMENT_ONLY
        }

        comments.add(
            ApprovalComment(
                id = comment.id,
                commenter = comment.commenter ?: User(id = "0", realName = "未知用户", position = "未知"),
                content = comment.content, // 不再添加用户名前缀，因为新的UI会单独显示用户信息
                commentTime = safeCommentTime,
                commentType = mappedCommentType
            )
        )
    }
    
    // 移除从流转记录自动生成的意见，流转记录只用于显示审批流程，不在历史意见中显示
    
    // 移除自动生成的最终审批意见，只显示用户主动添加的意见
    
    // 按时间排序，最新的在前
    return comments.sortedByDescending { it.commentTime }
}



// 根据Approval对象创建ApprovalRequest对象
private fun convertToApprovalRequest(approval: Approval): ApprovalRequest {
    Log.d("ApprovalDetailScreen", "转换Approval到ApprovalRequest")
    Log.d("ApprovalDetailScreen", "原始approval.createTime: '${approval.createTime}'")
    Log.d("ApprovalDetailScreen", "原始approval.updateTime: '${approval.updateTime}'")

    val submitTime = approval.createTime ?: ""
    Log.d("ApprovalDetailScreen", "设置submitTime为: '$submitTime'")

    return ApprovalRequest(
        id = approval.id,
        requestNumber = "REQ-${approval.id.takeLast(8)}",
        title = approval.title,
        status = approval.status,
        priority = ApprovalPriority.NORMAL,
        applicant = approval.applicant,
        submitTime = submitTime,
        description = approval.description,
        attachments = approval.attachments ?: emptyList(), // 从原始申请数据中获取附件
        approvalSteps = buildApprovalSteps(approval), // 使用新的方法构建完整流程
        comments = buildCommentsFromTransferHistory(approval), // 从流转历史生成历史意见
        transferRecords = approval.transferHistory ?: emptyList() // 流转记录
    )
}

// 创建默认的空审批请求
private fun createEmptyApprovalRequest(currentUser: User?): ApprovalRequest {
    return ApprovalRequest(
        id = "empty",
        requestNumber = "暂无数据",
        title = "暂无审批申请",
        status = ApprovalStatus.PENDING,
        priority = ApprovalPriority.NORMAL,
        applicant = currentUser ?: User(id = "0", realName = "未知", position = "未知"),
        submitTime = "",
        description = "当前没有可查看的审批申请，请先提交申请。",
        attachments = emptyList(),
        approvalSteps = emptyList(),
        comments = emptyList(),
        transferRecords = emptyList()
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApprovalDetailScreen(
    navController: NavController,
    approvalId: String? = null,
    authViewModel: AuthViewModel = viewModel(LocalContext.current as ComponentActivity),
    approvalViewModel: ApprovalViewModel = viewModel(LocalContext.current as ComponentActivity)
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // 收集ViewModel中的状态
    val currentUser by authViewModel.currentUser.collectAsState()
    val isLoading = approvalViewModel.isLoading
    val errorMessage = approvalViewModel.errorMessage
    val currentApproval by approvalViewModel.currentApproval.collectAsState()
    
    // 初始化时加载数据（支持缓存）
    LaunchedEffect(approvalId) {
        try {
            authViewModel.getCurrentUser()
            if (!approvalId.isNullOrBlank()) {
                Log.d("ApprovalDetailScreen", "🔍 开始加载审批详情: $approvalId")

                // 检查缓存状态（仅用于日志）
                val hasValidCache = DataCacheManager.hasValidApprovalDetailCache(approvalId)
                val cachedApproval = DataCacheManager.getCachedApprovalDetail(approvalId)

                if (hasValidCache && cachedApproval != null) {
                    Log.d("ApprovalDetailScreen", "✅ 发现有效缓存，将优先使用: ${cachedApproval.title}")
                } else if (cachedApproval != null) {
                    Log.d("ApprovalDetailScreen", "📋 发现过期缓存，将先显示后更新: ${cachedApproval.title}")
                } else {
                    Log.d("ApprovalDetailScreen", "🌐 无缓存数据，将从网络加载")
                }

                // 调用ViewModel方法（内部已包含完整的缓存逻辑）
                approvalViewModel.getApprovalById(approvalId)
            } else {
                Log.d("ApprovalDetailScreen", "开始加载相关审批")
                approvalViewModel.getRelatedApprovals()
            }
        } catch (e: Exception) {
            Log.e("ApprovalDetailScreen", "加载数据时出错", e)
        }
    }
    
    // 根据原始数据转换为ApprovalRequest用于显示
    val approval = remember(currentApproval, currentUser) {
        currentApproval?.let {
            Log.d("ApprovalDetailScreen", "转换审批数据，附件数量: ${it.attachments?.size ?: 0}")
            it.attachments?.forEachIndexed { index, attachment ->
                Log.d("ApprovalDetailScreen", "附件[$index]: id=${attachment.id}")
                Log.d("ApprovalDetailScreen", "  fileName=${attachment.fileName}")
                Log.d("ApprovalDetailScreen", "  fileSize=${attachment.fileSize}")
                Log.d("ApprovalDetailScreen", "  fileType=${attachment.fileType}")
                Log.d("ApprovalDetailScreen", "  uploadTime=${attachment.uploadTime}")
            }
            convertToApprovalRequest(it)
        } ?: createEmptyApprovalRequest(currentUser)
    }
    
    // 判断当前用户是否有审批权限 - 支持并行审批
    val canApprove = remember(currentUser, currentApproval) {
        Log.d("ApprovalDetailScreen", "=== 审批权限检查 ===")
        Log.d("ApprovalDetailScreen", "当前用户ID: ${currentUser?.id}")
        Log.d("ApprovalDetailScreen", "审批状态: ${currentApproval?.status}")
        Log.d("ApprovalDetailScreen", "审批ID: ${currentApproval?.id}")
        Log.d("ApprovalDetailScreen", "审批步骤数量: ${currentApproval?.steps?.size ?: 0}")

        currentApproval?.steps?.forEachIndexed { index, step ->
            Log.d("ApprovalDetailScreen", "步骤[$index]: 审批人=${step.approver.realName}(${step.approver.id}), 状态=${step.status}")
            Log.d("ApprovalDetailScreen", "  用户ID比较: '${step.approver.id}' vs '${currentUser?.id}'")
            Log.d("ApprovalDetailScreen", "  字符串比较: ${step.approver.id.toString() == currentUser?.id.toString()}")
            Log.d("ApprovalDetailScreen", "  状态匹配: ${step.status == "CURRENT" || step.status == "PENDING"}")
        }

        val hasPermission = currentUser?.id != null &&
                    currentApproval?.status == ApprovalStatus.PENDING &&
                    currentApproval?.id != null &&
                    currentApproval?.id != "empty" &&
                    // 并行审批：检查当前用户是否在审批步骤中且状态为CURRENT
                    currentApproval?.steps?.any { step ->
                        // 强制类型转换确保ID比较正确
                        step.approver.id.toString() == currentUser?.id.toString() &&
                        (step.status == "CURRENT" || step.status == "PENDING") // 兼容两种状态
                    } == true

        Log.d("ApprovalDetailScreen", "最终审批权限: $hasPermission")
        Log.d("ApprovalDetailScreen", "=== 审批权限检查结束 ===")

        hasPermission
    }
    
    // 获取导航返回的选择结果
    val selectedUserId = navController.currentBackStackEntry
        ?.savedStateHandle
        ?.get<String>("selected_transfer_user_id")

    // 其他状态管理
    var showApprovalDialog by remember { mutableStateOf(false) }
    var showRejectDialog by remember { mutableStateOf(false) }
    var showTransferDialog by remember { mutableStateOf(false) }
    var showCommentDialog by remember { mutableStateOf(false) }
    var approvalComment by remember { mutableStateOf("") }
    var transferUserId by remember { mutableStateOf("") }
    var commentText by remember { mutableStateOf("") }
    var showSuccessDialog by remember { mutableStateOf(false) }
    var successMessage by remember { mutableStateOf("") }

    // 当选择了用户后，延迟打开流转对话框，确保页面完全返回
    LaunchedEffect(selectedUserId) {
        if (!selectedUserId.isNullOrEmpty()) {
            // 延迟一小段时间，确保选择页面完全消失
            kotlinx.coroutines.delay(100)
            showTransferDialog = true
            transferUserId = selectedUserId
            // 清理savedStateHandle，避免重复触发
            navController.currentBackStackEntry?.savedStateHandle?.remove<String>("selected_transfer_user_id")
        }
    }

    // 处理审批通过
    val handleApprove = {
        if (approval.id != "empty") {
            // 直接调用ViewModel方法（ViewModel内部已经处理协程）
            approvalViewModel.approveApproval(
                approvalId = approval.id,
                comment = approvalComment
            ) { success ->
                if (success) {
                    showApprovalDialog = false
                    approvalComment = ""

                    // 清除相关缓存，确保主页显示最新数据
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_PENDING)
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)

                    // 直接跳转到主页，不显示成功对话框
                    navController.navigate(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                        popUpTo(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                            inclusive = false
                        }
                    }
                }
            }
        }
    }
    
    // 处理审批拒绝
    val handleReject = {
        if (approval.id != "empty") {
            // 直接调用ViewModel方法（ViewModel内部已经处理协程）
            approvalViewModel.rejectApproval(
                approvalId = approval.id,
                comment = approvalComment
            ) { success ->
                if (success) {
                    showRejectDialog = false
                    approvalComment = ""

                    // 清除相关缓存，确保主页显示最新数据
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_PENDING)
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                    DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)

                    // 直接跳转到主页，不显示成功对话框
                    navController.navigate(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                        popUpTo(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                            inclusive = false
                        }
                    }
                }
            }
        }
    }
    
    // 处理审批流转
    val handleTransfer = {
        if (approval.id != "empty" && transferUserId.isNotBlank()) {
            coroutineScope.launch {
                approvalViewModel.transferApproval(
                    approvalId = approval.id,
                    toUserId = transferUserId,
                    comment = approvalComment
                ) { success ->
                    if (success) {
                        showTransferDialog = false
                        approvalComment = ""
                        transferUserId = ""

                        // 清除相关缓存，确保主页显示最新数据
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_PENDING)
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.PENDING_APPROVALS)
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.HISTORY_APPROVALS)
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_APPROVALS)
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.MY_APPROVALS)
                        DataCacheManager.forceRefresh(DataCacheManager.Keys.HOME_MY_APPROVALS)

                        // 直接跳转到主页，不显示成功对话框
                        navController.navigate(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                            popUpTo(com.oaapproval.system.ui.navigation.Screen.EmployeeHome.route) {
                                inclusive = false
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 处理添加评论
    val handleAddComment = {
        if (approval.id != "empty" && commentText.isNotBlank()) {
            // 直接调用ViewModel方法（ViewModel内部已经处理协程）
            approvalViewModel.addComment(
                approvalId = approval.id,
                comment = commentText
            ) { success ->
                if (success) {
                    showCommentDialog = false
                    commentText = ""
                    successMessage = "评论已添加"
                    showSuccessDialog = true
                    // 重新加载数据
                    approvalViewModel.getApprovalById(approval.id)
                    // 短暂延迟后自动关闭成功对话框
                    coroutineScope.launch {
                        delay(1500)
                        showSuccessDialog = false
                    }
                }
            }
        }
    }

    // 成功操作对话框
    if (showSuccessDialog) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.5f))
                .clickable { showSuccessDialog = false },
            contentAlignment = Alignment.Center
        ) {
            Card(
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
                modifier = Modifier
                    .width(200.dp)
                    .wrapContentHeight()
                    .clickable { /* 阻止点击穿透 */ }
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.padding(24.dp)
                ) {
                    // 对号图标
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color(0xFF16A34A),
                        modifier = Modifier.size(60.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 成功文字
                    Text(
                        text = successMessage,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }

    MainScaffold(
        navController = navController,
        title = "审批详情"
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (errorMessage != null) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = errorMessage,
                            color = Color.Red
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                if (!approvalId.isNullOrBlank()) {
                                    approvalViewModel.getApprovalById(approvalId)
                                }
                            }
                        ) {
                            Text("重试")
                        }
                    }
                }
            } else {
            // 主内容区域
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                contentPadding = PaddingValues(
                    start = 16.dp,
                    end = 16.dp,
                    top = 20.dp,  // 增加顶部间距，与导航栏保持距离
                    bottom = 16.dp
                ),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 1. 顶部状态区（已移除）
                // item {
                //     ApprovalStatusCard(approval = approval)
                // }

                // 2. 申请内容区（核心信息）
                item {
                    ApprovalContentCard(approval = approval)
                }

                // 3. 附件区（始终显示，用于调试）
                item {
                    // 添加调试日志
                    Log.d("ApprovalDetailScreen", "附件数据检查:")
                    Log.d("ApprovalDetailScreen", "  approval.attachments = ${approval.attachments}")
                    Log.d("ApprovalDetailScreen", "  attachments.size = ${approval.attachments?.size ?: 0}")
                    Log.d("ApprovalDetailScreen", "  isNullOrEmpty = ${approval.attachments.isNullOrEmpty()}")

                    if (!approval.attachments.isNullOrEmpty()) {
                        val safeAttachments = approval.attachments.filter { attachment ->
                            !attachment.id.isNullOrBlank()
                        }.map { attachment ->
                            attachment.copy(
                                fileName = attachment.fileName ?: "未知文件",
                                fileSize = attachment.fileSize ?: "未知大小",
                                fileType = attachment.fileType ?: "未知类型",
                                uploadTime = attachment.uploadTime ?: "未知时间"
                            )
                        }
                        Log.d("ApprovalDetailScreen", "  safeAttachments.size = ${safeAttachments.size}")
                        AttachmentsCard(attachments = safeAttachments, approvalId = approval.id)
                    } else {
                        // 显示调试信息的附件卡片
                        AttachmentsCard(attachments = emptyList(), approvalId = approval.id)
                    }
                }

                // 4. 简化的进展信息（可折叠）
                item {
                    ApprovalProgressCard(approval = approval)
                }

                // 5. 全部留言卡片
                item {
                    AllCommentsCard(approval = approval)
                }

                // 底部间距，为固定操作按钮留出空间
                item {
                    com.oaapproval.system.ui.utils.AdaptiveBottomSpacer(extraHeight = 64.dp)
                }
            }
        
        // 底部固定操作按钮
                if (approval.id != "empty") {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
                        color = MaterialTheme.colorScheme.surface,
            shadowElevation = 8.dp
        ) {
            Column(
                            modifier = Modifier.padding(
                                start = 16.dp,
                                end = 16.dp,
                                top = 8.dp,
                                bottom = SystemBarsUtils.getSafeBottomPadding(extraPadding = 8.dp)  // 使用统一的自适应底部间距
                            )
            ) {
                // 操作按钮显示逻辑
                    if (canApprove) {
                                // 显示审批操作按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                                    // 转发按钮
                                    OutlinedButton(
                                        onClick = { showTransferDialog = true },
                                modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.outlinedButtonColors(
                                            contentColor = MaterialTheme.colorScheme.primary
                                        )
                                    ) {
                                    Icon(
                                            imageVector = Icons.AutoMirrored.Filled.Send,
                                            contentDescription = "流转"
                                        )
                                    }
                                    
                                    // 拒绝按钮
                                    OutlinedButton(
                                onClick = { showRejectDialog = true },
                                modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.outlinedButtonColors(
                                            contentColor = Color.Red
                                        )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                            contentDescription = "拒绝"
                                        )
                                    }
                                    
                                    // 同意按钮
                            Button(
                                        onClick = { showApprovalDialog = true },
                                modifier = Modifier.weight(1f),
                                colors = ButtonDefaults.buttonColors(
                                            containerColor = MaterialTheme.colorScheme.primary
                                        )
                            ) {
                                Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = "同意"
                                        )
                                    }
                                }
                            } else {
                                // 显示评论按钮
                Button(
                    onClick = { showCommentDialog = true },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.primary
                                    )
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Comment,
                                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("添加评论")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 审批对话框
    if (showApprovalDialog) {
        Dialog(onDismissRequest = { showApprovalDialog = false }) {
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                    Text(
                        text = "审批通过",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    OutlinedTextField(
                        value = approvalComment,
                        onValueChange = { approvalComment = it },
                        label = { Text("审批意见 (可选)") },
                        placeholder = { Text("请输入审批意见...") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                Row(
                    modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { showApprovalDialog = false }
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Button(
                            onClick = handleApprove,
                            enabled = !isLoading
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("确认通过")
                            }
                        }
                    }
                }
            }
        }
    }
    
    // 拒绝对话框
    if (showRejectDialog) {
        Dialog(onDismissRequest = { showRejectDialog = false }) {
                    Surface(
                shape = RoundedCornerShape(16.dp),
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                            Text(
                        text = "拒绝审批",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    OutlinedTextField(
                        value = approvalComment,
                        onValueChange = { approvalComment = it },
                        label = { Text("拒绝原因 (必填)") },
                        placeholder = { Text("请输入拒绝原因...") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3,
                        isError = approvalComment.isBlank()
                    )
                    
                    if (approvalComment.isBlank()) {
                        Text(
                            text = "拒绝时必须填写原因",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { showRejectDialog = false }
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                            onClick = handleReject,
                            enabled = approvalComment.isNotBlank() && !isLoading,
                        colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Red
                            )
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("确认拒绝")
                        }
                    }
                }
            }
        }
    }
}

    // 流转对话框
    if (showTransferDialog) {
        Dialog(onDismissRequest = { showTransferDialog = false }) {
        Surface(
                shape = RoundedCornerShape(16.dp),
                color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                        Text(
                            text = "流转审批",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 查找用户信息
                    val context = LocalContext.current
                    var selectedUserInfo by remember { mutableStateOf<User?>(null) }
                    
                    // 使用AuthViewModel获取所有用户
                    val authViewModel: AuthViewModel = viewModel()
                    val allUsers by authViewModel.allUsers.collectAsState()
                    
                    // 当transferUserId变化时，异步获取用户信息
                    LaunchedEffect(transferUserId, allUsers) {
                        if (transferUserId.isNotBlank()) {
                            // 在后台线程查找匹配的用户，避免阻塞UI
                            withContext(kotlinx.coroutines.Dispatchers.Default) {
                                val foundUser = allUsers.find { it.id == transferUserId }
                                // 切回主线程更新UI
                                withContext(kotlinx.coroutines.Dispatchers.Main) {
                                    selectedUserInfo = foundUser
                                }
                            }
                        }
                    }
                    
                    // 确保已加载所有用户数据
                    LaunchedEffect(Unit) {
                        authViewModel.getAllUsers()
                    }
                    
                    // 流转人选择器
                    OutlinedButton(
                        onClick = {
                            // 先关闭流转对话框，然后导航到选择用户界面
                            showTransferDialog = false
                            val applicantId = approval.applicant?.id ?: ""
                            navController.navigate("select_user_for_transfer?excludeUserId=$applicantId")
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                            Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = null
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                            Text(
                            text = if (selectedUserInfo != null)
                                "已选择: ${selectedUserInfo!!.realName} (${selectedUserInfo!!.position ?: "员工"})"
                            else
                                "选择流转对象"
                            )
                        }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    OutlinedTextField(
                        value = approvalComment,
                        onValueChange = { approvalComment = it },
                        label = { Text("流转说明 (可选)") },
                        placeholder = { Text("请输入流转说明...") },
                        modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { showTransferDialog = false }
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Button(
                            onClick = handleTransfer,
                            enabled = transferUserId.isNotBlank() && !isLoading
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("确认流转")
                            }
                        }
                                }
                            }
                        }
                    }
                }
                
    // 评论对话框
    if (showCommentDialog) {
        Dialog(onDismissRequest = { showCommentDialog = false }) {
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = MaterialTheme.colorScheme.surface
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                Text(
                        text = "添加评论",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                        value = commentText,
                        onValueChange = { commentText = it },
                        label = { Text("评论内容") },
                        placeholder = { Text("请输入评论内容...") },
                    modifier = Modifier.fillMaxWidth(),
                        minLines = 3
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { showCommentDialog = false }
                        ) {
                            Text("取消")
                        }
                        
                        Spacer(modifier = Modifier.width(8.dp))
                    
                    Button(
                            onClick = handleAddComment,
                            enabled = commentText.isNotBlank() && !isLoading
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Text("提交评论")
                            }
                        }
                    }
                }
            }
        }
    }
}

// 1. 顶部状态卡片
@Composable
private fun ApprovalStatusCard(approval: ApprovalRequest) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),  // 与其他卡片保持一致的圆角
        color = Color.White,  // 与其他卡片保持一致的白色背景
        shadowElevation = 2.dp  // 与其他卡片保持一致的阴影效果
    ) {
        Column(
            modifier = Modifier.padding(20.dp)  // 与其他卡片保持一致的内边距
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                // 状态图标
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(
                            when (approval.status) {
                                ApprovalStatus.PENDING -> Color(0xFF3B82F6)
                                ApprovalStatus.APPROVED -> Color(0xFF10B981)
                                ApprovalStatus.REJECTED -> Color(0xFFEF4444)
                            }
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = when (approval.status) {
                            ApprovalStatus.PENDING -> "🟡"
                            ApprovalStatus.APPROVED -> "✅"
                            ApprovalStatus.REJECTED -> "❌"
                        },
                        fontSize = 12.sp
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 状态文字
                Text(
                    text = when (approval.status) {
                        ApprovalStatus.PENDING -> "审批中"
                        ApprovalStatus.APPROVED -> "已通过"
                        ApprovalStatus.REJECTED -> "已拒绝"
                    },
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = when (approval.status) {
                        ApprovalStatus.PENDING -> Color(0xFF3B82F6)
                        ApprovalStatus.APPROVED -> Color(0xFF10B981)
                        ApprovalStatus.REJECTED -> Color(0xFFEF4444)
                    }
                )
            }

            // 当前审批人信息（仅在审批中时显示）
            if (approval.status == ApprovalStatus.PENDING) {
                val currentApprovers = getCurrentApprovers(approval.approvalSteps)
                if (currentApprovers.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))

                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(8.dp),
                        color = Color(0xFFF8FAFC)  // 使用浅灰色背景，与内容区域形成层次
                    ) {
                        Text(
                            text = "当前审批人：${currentApprovers.joinToString("、") { it.approver.realName }}",
                            fontSize = 14.sp,
                            color = Color(0xFF64748B),  // 使用中等灰色文字
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }
            }
        }
    }
}

// 2. 申请内容卡片
@Composable
private fun ApprovalContentCard(approval: ApprovalRequest) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 申请标题
            Text(
                text = approval.title,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF0F172A),
                modifier = Modifier.padding(bottom = 12.dp)
            )

            // 申请人信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            ) {
                // 申请人头像
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFE0F2FE)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = approval.applicant.realName.firstOrNull()?.toString() ?: "?",
                        color = Color(0xFF3B82F6),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold
                    )
                }

                Spacer(modifier = Modifier.width(12.dp))

                // 申请人详细信息
                Column {
                    Text(
                        text = approval.applicant.realName,
                        fontSize = 15.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF0F172A)
                    )
                    Text(
                        text = approval.submitTime,
                        fontSize = 13.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }

            // 分隔线
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(1.dp)
                    .background(Color(0xFFE2E8F0))
                    .padding(bottom = 16.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 申请内容
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp),
                color = Color(0xFFF8FAFC)
            ) {
                Text(
                    text = approval.description,
                    fontSize = 14.sp,
                    color = Color(0xFF0F172A),
                    lineHeight = 20.sp,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

@Composable
private fun ApplicantInfoCard(approval: ApprovalRequest) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {

            // 上半部分：申请人信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 使用与"我的"页面一致的头像样式
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFE0F2FE)),
                        contentAlignment = Alignment.Center
                    ) {
                        // 显示姓名首字母或第一个汉字作为默认头像
                        val firstLetter = approval.applicant.realName.firstOrNull()?.toString() ?: "?"
                        Text(
                            text = firstLetter,
                            color = Color(0xFF3B82F6),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Column {
                        Text(
                            text = approval.applicant.realName,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF0F172A)
                        )
                        Text(
                            text = approval.applicant.position ?: "员工",
                            fontSize = 14.sp,
                            color = Color(0xFF64748B)
                        )
                        Text(
                            text = "申请时间：${formatSubmitTime(approval.submitTime)}",
                            fontSize = 12.sp,
                            color = Color(0xFF9CA3AF)
                        )
                    }
                }
                
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    // 状态标签
                    // 根据审批状态显示不同的图标和颜色
                    val (statusIcon, statusColor, backgroundColor) = when (approval.status) {
                        ApprovalStatus.PENDING -> Triple(
                            Icons.Default.AccessTime,
                            Color(0xFFD97706),
                            Color(0xFFFEF3C7)
                        )
                        ApprovalStatus.APPROVED -> Triple(
                            Icons.Default.CheckCircle,
                            Color(0xFF10B981),
                            Color(0xFFD1FAE5)
                        )
                        ApprovalStatus.REJECTED -> Triple(
                            Icons.Default.Cancel,
                            Color(0xFFEF4444),
                            Color(0xFFFEE2E2)
                        )
                        else -> Triple(
                            Icons.Default.Help,
                            Color(0xFF64748B),
                            Color(0xFFF1F5F9)
                        )
                    }

                    Surface(
                        shape = RoundedCornerShape(20.dp),
                        color = backgroundColor
                    ) {
                        // 只显示图标，不显示文字
                        Box(
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 6.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = statusIcon,
                                contentDescription = approval.status.displayName,
                                tint = statusColor,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 分隔线
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(),
                thickness = 1.dp,
                color = Color(0xFFF1F5F9)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
                        // 申请详情
            Text(
                text = approval.title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFF0F172A),
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 申请内容
            Column {
                Text(
                    text = "申请内容",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF64748B),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp),
                    color = Color(0xFFF8FAFC)
                ) {
                    Text(
                        text = approval.description,
                        fontSize = 14.sp,
                        color = Color(0xFF0F172A),
                        modifier = Modifier.padding(16.dp),
                        lineHeight = 20.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun AttachmentsCard(attachments: List<AttachmentData>, approvalId: String) {
    // 添加调试日志
    Log.d("AttachmentsCard", "渲染附件卡片，附件数量: ${attachments.size}, 审批ID: $approvalId")
    attachments.forEachIndexed { index, attachment ->
        Log.d("AttachmentsCard", "附件[$index]: ID=${attachment.id}, 文件名=${attachment.fileName}, 类型=${attachment.fileType}")
    }

    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.AttachFile,
                    contentDescription = null,
                    tint = Color(0xFF3B82F6),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "相关附件",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )
            }
            
            if (attachments.isEmpty()) {
                Text(
                    text = "暂无附件",
                    fontSize = 14.sp,
                    color = Color(0xFF9CA3AF),
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Spacer(modifier = Modifier.height(16.dp))
                // 使用安全的方式渲染附件列表
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    attachments.take(10).forEach { attachment ->
                        // 检查附件数据的有效性
                        if (attachment.id.isNotBlank() && !attachment.fileName.isNullOrBlank()) {
                            SafeAttachmentItem(
                                attachment = attachment,
                                approvalId = approvalId
                            )
                        } else {
                            // 显示错误的附件项
                            Text(
                                text = "附件数据异常: ${attachment.fileName ?: "未知文件"}",
                                color = Color.Red,
                                fontSize = 12.sp
                            )
                        }
                    }
                }

                // 如果附件数量超过10个，显示提示
                if (attachments.size > 10) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "还有 ${attachments.size - 10} 个附件未显示",
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
        }
    }
}

// 安全的附件项组件
@Composable
private fun SafeAttachmentItem(
    attachment: AttachmentData,
    approvalId: String,
    context: android.content.Context = LocalContext.current
) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                previewAttachment(context, attachment, approvalId)
            },
        shape = RoundedCornerShape(12.dp),
        color = Color(0xFFF8FAFC)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                // 文件类型图标 - 简化处理
                val fileTypeStr = (attachment.fileType ?: "").lowercase().trim()
                val (iconVector, iconColor) = when {
                    fileTypeStr.contains("pdf") -> Icons.Default.PictureAsPdf to Color(0xFFEF4444)
                    fileTypeStr.contains("excel") || fileTypeStr.contains("xlsx") -> Icons.Default.TableChart to Color(0xFF10B981)
                    fileTypeStr.contains("word") || fileTypeStr.contains("doc") -> Icons.Default.Description to Color(0xFF3B82F6)
                    fileTypeStr.contains("图片") || fileTypeStr.contains("jpg") ||
                    fileTypeStr.contains("jpeg") || fileTypeStr.contains("png") -> Icons.Default.Image to Color(0xFF7C3AED)
                    else -> Icons.AutoMirrored.Filled.InsertDriveFile to Color(0xFF64748B)
                }

                Icon(
                    imageVector = iconVector,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column {
                    Text(
                        text = attachment.fileName ?: "未知文件",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF0F172A),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = "${attachment.fileSize ?: "未知大小"} · 上传于 ${formatUploadTime(attachment.uploadTime)}",
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }


        }
    }
}

@Composable
private fun AttachmentItem(attachment: AttachmentData) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        color = Color(0xFFF8FAFC)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                // 文件类型图标 - 安全处理
                val fileTypeStr = attachment.fileType?.lowercase()?.trim() ?: ""
                val (iconVector, iconColor) = when {
                    fileTypeStr.contains("pdf") -> Icons.Default.PictureAsPdf to Color(0xFFEF4444)
                    fileTypeStr.contains("excel") || fileTypeStr.contains("xlsx") -> Icons.Default.TableChart to Color(0xFF10B981)
                    fileTypeStr.contains("word") || fileTypeStr.contains("doc") -> Icons.Default.Description to Color(0xFF3B82F6)
                    fileTypeStr.contains("图片") || fileTypeStr.contains("jpg") ||
                    fileTypeStr.contains("jpeg") || fileTypeStr.contains("png") -> Icons.Default.Image to Color(0xFF7C3AED)
                    else -> Icons.AutoMirrored.Filled.InsertDriveFile to Color(0xFF64748B)
                }
                
                Icon(
                    imageVector = iconVector,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column {
                    Text(
                        text = attachment.fileName ?: "未知文件",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF0F172A),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        text = "${attachment.fileSize ?: "未知大小"} · 上传于 ${formatUploadTime(attachment.uploadTime)}",
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }
            

        }
    }
}

// 4. 简化的进展信息卡片
@Composable
private fun ApprovalProgressCard(approval: ApprovalRequest) {
    var isExpanded by remember { mutableStateOf(false) }
    val steps = approval.approvalSteps

    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // 标题和展开按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "审批进展",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )

                TextButton(
                    onClick = { isExpanded = !isExpanded }
                ) {
                    Text(
                        text = if (isExpanded) "收起详细 ▲" else "查看详细 ▼",
                        fontSize = 14.sp,
                        color = Color(0xFF3B82F6)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 统计信息 - 只统计真正的审批步骤，排除申请提交步骤
            val approvalSteps = steps.filter { it.stepName != "申请提交" }

            // 统计各种状态：TRANSFERRED状态算作已完成（已处理）
            val completedCount = approvalSteps.count {
                it.status == StepStatus.COMPLETED ||
                it.status == StepStatus.REJECTED ||
                it.status == StepStatus.TRANSFERRED
            }
            val currentCount = approvalSteps.count { it.status == StepStatus.CURRENT }
            val pendingCount = approvalSteps.count { it.status == StepStatus.PENDING }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                ProgressStatItem(
                    title = "已审批",
                    count = completedCount,
                    color = Color(0xFF10B981),
                    modifier = Modifier.weight(1f)
                )
                ProgressStatItem(
                    title = "进行中",
                    count = currentCount,
                    color = Color(0xFF3B82F6),
                    modifier = Modifier.weight(1f)
                )
                ProgressStatItem(
                    title = "待审批",
                    count = pendingCount,
                    color = Color(0xFF64748B),
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 当前状态概览（已移除）
            // ApprovalStatusOverview(approval = approval)

            // 详细节点信息（可展开）
            AnimatedVisibility(visible = isExpanded) {
                Column {
                    Spacer(modifier = Modifier.height(16.dp))

                    // 显示详细的节点信息
                    ApprovalNodesDetail(approval = approval)
                }
            }
        }
    }
}

@Composable
private fun ProgressStatItem(
    title: String,
    count: Int,
    color: Color,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFF8FAFC)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = count.toString(),
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                color = color
            )
            Text(
                text = title,
                fontSize = 12.sp,
                color = Color(0xFF64748B)
            )
        }
    }
}

@Composable
private fun TimelineItem(
    step: ApprovalStep,
    isLast: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 时间线指示器
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(20.dp)
                    .clip(CircleShape)
                    .background(
                        when (step.status) {
                            StepStatus.COMPLETED -> Color(0xFF10B981)
                            StepStatus.CURRENT -> Color(0xFF3B82F6)
                            StepStatus.PENDING -> Color(0xFFE5E7EB)
                            StepStatus.REJECTED -> Color(0xFFEF4444)
                            StepStatus.TRANSFERRED -> Color(0xFFF59E0B)
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = when (step.status) {
                        StepStatus.COMPLETED -> "✓"
                        StepStatus.CURRENT -> "⏳"
                        StepStatus.PENDING -> "⏳"
                        StepStatus.REJECTED -> "✗"
                        StepStatus.TRANSFERRED -> "↗"
                    },
                    fontSize = 10.sp,
                    color = if (step.status == StepStatus.PENDING) Color(0xFF64748B) else Color.White
                )
            }

            if (!isLast) {
                Box(
                    modifier = Modifier
                        .width(1.dp)
                        .height(32.dp)
                        .background(Color(0xFFE5E7EB))
                )
            }
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 步骤内容
        Column(modifier = Modifier.weight(1f)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = step.stepName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF0F172A)
                )
                if (step.processTime != null) {
                    Text(
                        text = step.processTime,
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
            }

            if (step.comment.isNotEmpty()) {
                Text(
                    text = step.comment,
                    fontSize = 12.sp,
                    color = Color(0xFF64748B),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
private fun ApprovalProcessCard(steps: List<ApprovalStep>) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Route,
                    contentDescription = null,
                    tint = Color(0xFF9333EA),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "审批流程",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )
            }
            
            Column(
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                steps.forEach { step ->
                    ApprovalStepItem(step = step, isLast = step == steps.last())
                }
            }
        }
    }
}

@Composable
private fun ApprovalStepItem(step: ApprovalStep, isLast: Boolean) {
    Row(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 时间线指示器
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = when (step.status) {
                            StepStatus.COMPLETED -> Color(0xFF10B981)
                            StepStatus.CURRENT -> Color(0xFF3B82F6)
                            StepStatus.PENDING -> Color(0xFFE5E7EB)
                            StepStatus.REJECTED -> Color(0xFFEF4444)
                            StepStatus.TRANSFERRED -> Color(0xFFF59E0B)
                        },
                        shape = CircleShape
                    )
            )
            
            if (!isLast) {
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(40.dp)
                        .background(Color(0xFFE5E7EB))
                )
            }
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 步骤内容
        Surface(
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(8.dp),
            color = when (step.status) {
                StepStatus.COMPLETED -> Color(0xFFF0FDF4)
                StepStatus.CURRENT -> Color(0xFFEFF6FF)
                StepStatus.PENDING -> Color(0xFFF8FAFC)
                StepStatus.REJECTED -> Color(0xFFFEF2F2)
                StepStatus.TRANSFERRED -> Color(0xFFFEF3C7)
            }
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = step.stepName,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                    Text(
                        text = step.processTime ?: step.estimatedProcessTime,
                        fontSize = 12.sp,
                        color = Color(0xFF64748B)
                    )
                }
                
                if (step.status != StepStatus.PENDING) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = step.comment,
                        fontSize = 14.sp,
                        color = Color(0xFF64748B)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val (statusIcon, statusColor, statusText) = when (step.status) {
                        StepStatus.COMPLETED -> Triple(Icons.Default.CheckCircle, Color(0xFF10B981), "已完成")
                        StepStatus.CURRENT -> Triple(Icons.Default.AccessTime, Color(0xFF3B82F6), "处理中")
                        StepStatus.PENDING -> Triple(Icons.Default.Schedule, Color(0xFF64748B), "等待审批")
                        StepStatus.REJECTED -> Triple(Icons.Default.Cancel, Color(0xFFEF4444), "已拒绝")
                        StepStatus.TRANSFERRED -> Triple(Icons.Default.SwapHoriz, Color(0xFFF59E0B), "已流转")
                    }
                    
                    Icon(
                        imageVector = statusIcon,
                        contentDescription = null,
                        tint = statusColor,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = statusText,
                        fontSize = 12.sp,
                        color = statusColor
                    )
                }
            }
        }
    }
}

// 5. 全部留言卡片
@Composable
private fun AllCommentsCard(approval: ApprovalRequest) {
    // 显示所有类型的评论和留言
    val allComments = approval.comments ?: emptyList()

    if (allComments.isNotEmpty()) {
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            color = Color.White,
            shadowElevation = 2.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // 标题行
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 16.dp)
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.Comment,
                        contentDescription = null,
                        tint = Color(0xFF8B5CF6),
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "全部留言",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color(0xFF0F172A)
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Text(
                        text = "${allComments.size}条",
                        fontSize = 14.sp,
                        color = Color(0xFF64748B)
                    )
                }

                // 留言列表
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    allComments.forEach { comment ->
                        CommentItem(comment = comment)
                    }
                }
            }
        }
    }
}

// 单个留言项组件
@Composable
private fun CommentItem(comment: ApprovalComment) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFF8FAFC),
        border = BorderStroke(1.dp, Color(0xFFE2E8F0))
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 留言人信息和时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = comment.commenter.realName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF374151)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = comment.commenter.position ?: "未知职位",
                        fontSize = 12.sp,
                        color = Color(0xFF9CA3AF)
                    )
                }
                Text(
                    text = formatDateTime(comment.commentTime),
                    fontSize = 12.sp,
                    color = Color(0xFF9CA3AF)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // 留言内容
            Text(
                text = comment.content,
                fontSize = 14.sp,
                color = Color(0xFF0F172A),
                lineHeight = 20.sp
            )
        }
    }
}

// 详细步骤项（用于展开的详细记录）
@Composable
private fun DetailedStepItem(step: ApprovalStep) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 状态指示器
        Box(
            modifier = Modifier
                .size(8.dp)
                .clip(CircleShape)
                .background(
                    when (step.status) {
                        StepStatus.COMPLETED -> Color(0xFF10B981)
                        StepStatus.CURRENT -> Color(0xFF3B82F6)
                        StepStatus.PENDING -> Color(0xFFE5E7EB)
                        StepStatus.REJECTED -> Color(0xFFEF4444)
                        StepStatus.TRANSFERRED -> Color(0xFFF59E0B)
                    }
                )
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 审批人头像
        Box(
            modifier = Modifier
                .size(32.dp)
                .clip(CircleShape)
                .background(Color(0xFFE0F2FE)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = step.approver.realName.firstOrNull()?.toString() ?: "?",
                color = Color(0xFF3B82F6),
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        // 审批人信息
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = step.approver.realName,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = step.approver.position ?: "员工",
                fontSize = 12.sp,
                color = Color(0xFF64748B),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // 状态和时间
        Column(horizontalAlignment = Alignment.End) {
            Surface(
                shape = RoundedCornerShape(4.dp),
                color = when (step.status) {
                    StepStatus.COMPLETED -> Color(0xFFDCFCE7)
                    StepStatus.CURRENT -> Color(0xFFDBEAFE)
                    StepStatus.PENDING -> Color(0xFFF1F5F9)
                    StepStatus.REJECTED -> Color(0xFFFECDD3)
                    StepStatus.TRANSFERRED -> Color(0xFFFEF3C7)
                }
            ) {
                Text(
                    text = when (step.status) {
                        StepStatus.COMPLETED -> "已完成"
                        StepStatus.CURRENT -> "进行中"
                        StepStatus.PENDING -> "待审批"
                        StepStatus.REJECTED -> "已拒绝"
                        StepStatus.TRANSFERRED -> "已流转"
                    },
                    fontSize = 10.sp,
                    color = when (step.status) {
                        StepStatus.COMPLETED -> Color(0xFF166534)
                        StepStatus.CURRENT -> Color(0xFF1E40AF)
                        StepStatus.PENDING -> Color(0xFF64748B)
                        StepStatus.REJECTED -> Color(0xFFDC2626)
                        StepStatus.TRANSFERRED -> Color(0xFFD97706)
                    },
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }

            if (step.processTime != null) {
                Text(
                    text = step.processTime,
                    fontSize = 10.sp,
                    color = Color(0xFF64748B),
                    modifier = Modifier.padding(top = 2.dp)
                )
            }
        }
    }
}

// 辅助函数
private fun getCurrentApprovers(steps: List<ApprovalStep>): List<ApprovalStep> {
    return steps.filter { it.status == StepStatus.CURRENT }
}

private fun extractKeySteps(steps: List<ApprovalStep>): List<ApprovalStep> {
    if (steps.isEmpty()) return emptyList()

    val keySteps = mutableListOf<ApprovalStep>()

    // 添加第一个步骤（申请提交）
    steps.firstOrNull()?.let { keySteps.add(it) }

    // 添加已完成的关键节点（每5个人一个节点）
    val completedSteps = steps.filter { it.status == StepStatus.COMPLETED || it.status == StepStatus.REJECTED }
    completedSteps.chunked(5).forEach { chunk ->
        if (chunk.size == 5) {
            keySteps.add(
                ApprovalStep(
                    id = "group_${chunk.first().id}",
                    stepName = "第${keySteps.size}轮审批通过 (${chunk.size}人)",
                    approver = chunk.first().approver,
                    status = StepStatus.COMPLETED,
                    processTime = chunk.last().processTime,
                    comment = ""
                )
            )
        }
    }

    // 添加当前审批步骤
    val currentSteps = steps.filter { it.status == StepStatus.CURRENT }
    if (currentSteps.isNotEmpty()) {
        keySteps.add(
            ApprovalStep(
                id = "current",
                stepName = "第${keySteps.size + 1}轮审批中",
                approver = currentSteps.first().approver,
                status = StepStatus.CURRENT,
                processTime = null,
                comment = currentSteps.joinToString("、") { it.approver.realName }
            )
        )
    }

    // 添加待审批概要
    val pendingCount = steps.count { it.status == StepStatus.PENDING }
    if (pendingCount > 0) {
        keySteps.add(
            ApprovalStep(
                id = "pending",
                stepName = "待审批",
                approver = steps.first { it.status == StepStatus.PENDING }.approver,
                status = StepStatus.PENDING,
                processTime = null,
                comment = "等待中..."
            )
        )
    }

    return keySteps
}



@Composable
private fun HistoryCommentsCard(comments: List<ApprovalComment>) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.Comment,
                    contentDescription = null,
                    tint = Color(0xFF6366F1),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "历史意见",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )
            }
            
            if (comments.isEmpty()) {
                Text(
                    text = "暂无历史意见",
                    fontSize = 14.sp,
                    color = Color(0xFF9CA3AF),
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    comments.forEach { comment ->
                        HistoryCommentItem(comment = comment)
                    }
                }
            }
        }
    }
}

@Composable
private fun HistoryCommentItem(comment: ApprovalComment) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        color = Color(0xFFEFF6FF)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 使用与"我的"页面一致的头像样式（小尺寸）
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFE0F2FE)),
                        contentAlignment = Alignment.Center
                    ) {
                        // 显示姓名首字母或第一个汉字作为默认头像
                        val firstLetter = comment.commenter.realName.firstOrNull()?.toString() ?: "?"
                        Text(
                            text = firstLetter,
                            color = Color(0xFF3B82F6),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "${comment.commenter.realName} (${comment.commenter.position})",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF0F172A)
                    )
                }

            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = comment.content,
                fontSize = 14.sp,
                color = Color(0xFF374151),
                lineHeight = 20.sp
            )
        }
    }
}

// 预览附件函数
private fun previewAttachment(context: android.content.Context, attachment: AttachmentData, approvalId: String) {
    val fileType = attachment.fileType?.lowercase()?.trim() ?: ""
    val fileName = attachment.fileName ?: "未知文件"

    Log.d("AttachmentPreview", "预览文件: $fileName, 类型: $fileType")

    try {
        when {
            // 图片文件预览
            isImageFile(fileType) -> {
                previewImage(context, attachment, approvalId)
            }
            // WPS文件预览（Word、Excel、PowerPoint）
            isWpsFile(fileType) -> {
                previewWpsFile(context, attachment, approvalId)
            }
            // PDF文件预览
            isPdfFile(fileType) -> {
                previewPdfFile(context, attachment, approvalId)
            }
            else -> {
                android.widget.Toast.makeText(
                    context,
                    "暂不支持预览此类型文件：$fileType",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览文件失败", e)
        android.widget.Toast.makeText(
            context,
            "预览失败：${e.message}",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
}

// 判断是否为图片文件
private fun isImageFile(fileType: String): Boolean {
    return fileType.contains("image") ||
           fileType.contains("jpg") ||
           fileType.contains("jpeg") ||
           fileType.contains("png") ||
           fileType.contains("gif") ||
           fileType.contains("bmp") ||
           fileType.contains("webp") ||
           fileType.contains("图片")
}

// 判断是否为WPS文件
private fun isWpsFile(fileType: String): Boolean {
    return fileType.contains("word") ||
           fileType.contains("doc") ||
           fileType.contains("docx") ||
           fileType.contains("excel") ||
           fileType.contains("xls") ||
           fileType.contains("xlsx") ||
           fileType.contains("powerpoint") ||
           fileType.contains("ppt") ||
           fileType.contains("pptx") ||
           fileType.contains("文档") ||
           fileType.contains("表格") ||
           fileType.contains("演示")
}

// 判断是否为PDF文件
private fun isPdfFile(fileType: String): Boolean {
    return fileType.contains("pdf")
}

// 预览图片
private fun previewImage(context: android.content.Context, attachment: AttachmentData, approvalId: String) {
    try {
        // 构建图片URL
        val imageUrl = buildAttachmentUrl(attachment, approvalId)
        Log.d("AttachmentPreview", "预览图片URL: $imageUrl")

        // 显示加载提示
        android.widget.Toast.makeText(context, "正在加载图片...", android.widget.Toast.LENGTH_SHORT).show()

        // 下载图片到本地缓存，然后预览
        downloadAndPreviewImage(context, imageUrl, attachment.fileName ?: "image")
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览图片失败", e)
        android.widget.Toast.makeText(context, "预览图片失败", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 预览WPS文件
private fun previewWpsFile(context: android.content.Context, attachment: AttachmentData, approvalId: String) {
    try {
        val fileUrl = buildAttachmentUrl(attachment, approvalId)
        Log.d("AttachmentPreview", "预览WPS文件URL: $fileUrl")

        // 显示加载提示
        android.widget.Toast.makeText(context, "正在加载文档...", android.widget.Toast.LENGTH_SHORT).show()

        // 下载文件到本地缓存，然后预览
        downloadAndPreviewFile(context, fileUrl, attachment.fileName ?: "document", getMimeType(attachment.fileType))
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览WPS文件失败", e)
        android.widget.Toast.makeText(context, "预览文档失败", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 预览PDF文件
private fun previewPdfFile(context: android.content.Context, attachment: AttachmentData, approvalId: String) {
    try {
        val fileUrl = buildAttachmentUrl(attachment, approvalId)
        Log.d("AttachmentPreview", "预览PDF文件URL: $fileUrl")

        // 显示加载提示
        android.widget.Toast.makeText(context, "正在加载PDF...", android.widget.Toast.LENGTH_SHORT).show()

        // 下载文件到本地缓存，然后预览
        downloadAndPreviewFile(context, fileUrl, attachment.fileName ?: "document.pdf", "application/pdf")
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览PDF文件失败", e)
        android.widget.Toast.makeText(context, "预览PDF失败", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 使用浏览器打开文件
private fun openInBrowser(context: android.content.Context, url: String) {
    try {
        val browserIntent = android.content.Intent(android.content.Intent.ACTION_VIEW, android.net.Uri.parse(url))
        browserIntent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(browserIntent)
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "使用浏览器打开失败", e)
        android.widget.Toast.makeText(context, "无法打开文件", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 构建附件预览URL
private fun buildAttachmentUrl(attachment: AttachmentData, approvalId: String): String {
    // 根据后端API构建正确的预览URL
    val baseUrl = "http://47.95.213.220:3000" // 你的服务器地址
    val url = "$baseUrl/api/approvals/$approvalId/attachments/${attachment.id}/preview"

    // 输出调试信息
    Log.d("AttachmentPreview", "构建预览URL: $url")
    Log.d("AttachmentPreview", "附件ID: ${attachment.id}, 审批ID: $approvalId")

    // 同时输出一个可以直接在浏览器中测试的URL
    android.util.Log.w("AttachmentPreview", "=== 可以在浏览器中测试的URL ===")
    android.util.Log.w("AttachmentPreview", url)
    android.util.Log.w("AttachmentPreview", "================================")

    return url
}

// 获取MIME类型
private fun getMimeType(fileType: String?): String {
    return when (fileType?.lowercase()?.trim()) {
        "doc", "docx" -> "application/msword"
        "xls", "xlsx" -> "application/vnd.ms-excel"
        "ppt", "pptx" -> "application/vnd.ms-powerpoint"
        "pdf" -> "application/pdf"
        "jpg", "jpeg" -> "image/jpeg"
        "png" -> "image/png"
        "gif" -> "image/gif"
        else -> "application/octet-stream"
    }
}

// 简化的附件卡片，避免复杂渲染导致的卡死问题
@Composable
private fun SimpleAttachmentsCard(attachmentCount: Int) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White,
        shadowElevation = 2.dp
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.AttachFile,
                    contentDescription = null,
                    tint = Color(0xFF3B82F6),
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "相关附件",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF0F172A)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (attachmentCount == 0) {
                Text(
                    text = "暂无附件",
                    fontSize = 14.sp,
                    color = Color(0xFF9CA3AF)
                )
            } else {
                Text(
                    text = "共有 $attachmentCount 个附件",
                    fontSize = 14.sp,
                    color = Color(0xFF64748B)
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "附件功能正在优化中，暂时无法显示详细信息",
                    fontSize = 12.sp,
                    color = Color(0xFF9CA3AF)
                )
            }
        }
    }
}

// 下载并预览图片
private fun downloadAndPreviewImage(context: android.content.Context, imageUrl: String, fileName: String) {
    // 使用协程在后台下载图片
    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
        try {
            Log.d("AttachmentPreview", "开始下载图片: $imageUrl")

            // 创建缓存目录
            val cacheDir = java.io.File(context.cacheDir, "attachments")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            // 生成本地文件名 - 使用原始文件名，但添加时间戳避免冲突
            val fileExtension = getFileExtension(fileName)
            val baseFileName = fileName.substringBeforeLast('.').ifEmpty { "image" }
            val safeFileName = sanitizeFileName(baseFileName)
            val localFileName = "${safeFileName}_${System.currentTimeMillis()}$fileExtension"
            val localFile = java.io.File(cacheDir, localFileName)

            // 下载图片
            val url = java.net.URL(imageUrl)
            val connection = url.openConnection()
            connection.connectTimeout = 10000 // 10秒超时
            connection.readTimeout = 30000 // 30秒读取超时

            connection.getInputStream().use { input ->
                localFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }

            Log.d("AttachmentPreview", "图片下载完成: ${localFile.absolutePath}")

            // 在主线程中预览图片
            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                previewLocalImage(context, localFile)
            }

        } catch (e: Exception) {
            Log.e("AttachmentPreview", "下载图片失败", e)
            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                android.widget.Toast.makeText(context, "下载图片失败: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }
}

// 预览本地图片
private fun previewLocalImage(context: android.content.Context, imageFile: java.io.File) {
    try {
        Log.d("AttachmentPreview", "预览本地图片: ${imageFile.absolutePath}")

        // 使用FileProvider创建安全的URI
        val uri = androidx.core.content.FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            imageFile
        )

        // 创建Intent预览图片
        val intent = android.content.Intent(android.content.Intent.ACTION_VIEW).apply {
            setDataAndType(uri, "image/*")
            addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        // 检查是否有应用可以处理此Intent
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
            Log.d("AttachmentPreview", "成功启动图片预览")
        } else {
            Log.e("AttachmentPreview", "没有找到可用的图片查看器")
            android.widget.Toast.makeText(context, "没有找到可用的图片查看器", android.widget.Toast.LENGTH_SHORT).show()
        }
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览本地图片失败", e)
        android.widget.Toast.makeText(context, "预览图片失败: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 下载并预览文件（通用方法）
private fun downloadAndPreviewFile(context: android.content.Context, fileUrl: String, fileName: String, mimeType: String) {
    // 使用协程在后台下载文件
    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
        try {
            Log.d("AttachmentPreview", "开始下载文件: $fileUrl")

            // 创建缓存目录
            val cacheDir = java.io.File(context.cacheDir, "attachments")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            // 生成本地文件名 - 使用原始文件名，但添加时间戳避免冲突
            val fileExtension = getFileExtension(fileName)
            val baseFileName = fileName.substringBeforeLast('.').ifEmpty { "document" }
            val safeFileName = sanitizeFileName(baseFileName)
            val localFileName = "${safeFileName}_${System.currentTimeMillis()}$fileExtension"
            val localFile = java.io.File(cacheDir, localFileName)

            // 下载文件
            val url = java.net.URL(fileUrl)
            val connection = url.openConnection()
            connection.connectTimeout = 10000 // 10秒超时
            connection.readTimeout = 30000 // 30秒读取超时

            connection.getInputStream().use { input ->
                localFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }

            Log.d("AttachmentPreview", "文件下载完成: ${localFile.absolutePath}")

            // 在主线程中预览文件
            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                previewLocalFile(context, localFile, mimeType)
            }

        } catch (e: Exception) {
            Log.e("AttachmentPreview", "下载文件失败", e)
            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                android.widget.Toast.makeText(context, "下载文件失败: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }
}

// 预览本地文件（通用方法）
private fun previewLocalFile(context: android.content.Context, file: java.io.File, mimeType: String) {
    try {
        Log.d("AttachmentPreview", "预览本地文件: ${file.absolutePath}, MIME类型: $mimeType")

        // 使用FileProvider创建安全的URI
        val uri = androidx.core.content.FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        // 创建Intent预览文件
        val intent = android.content.Intent(android.content.Intent.ACTION_VIEW).apply {
            setDataAndType(uri, mimeType)
            addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        // 检查是否有应用可以处理此Intent
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
            Log.d("AttachmentPreview", "成功启动文件预览")
        } else {
            Log.e("AttachmentPreview", "没有找到可用的应用来打开此文件类型: $mimeType")
            android.widget.Toast.makeText(context, "没有找到可用的应用来打开此文件", android.widget.Toast.LENGTH_SHORT).show()
        }
    } catch (e: Exception) {
        Log.e("AttachmentPreview", "预览本地文件失败", e)
        android.widget.Toast.makeText(context, "预览文件失败: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
    }
}

// 获取文件扩展名
private fun getFileExtension(fileName: String): String {
    val lastDot = fileName.lastIndexOf('.')
    return if (lastDot != -1) fileName.substring(lastDot) else ""
}

// 清理文件名，移除文件系统不支持的字符
private fun sanitizeFileName(fileName: String): String {
    // 移除或替换文件系统不支持的字符
    return fileName
        .replace("[\\\\/:*?\"<>|]".toRegex(), "_") // 替换Windows不支持的字符
        .replace("\\s+".toRegex(), "_") // 替换空格为下划线
        .take(50) // 限制长度，避免文件名过长
        .trim('_') // 移除首尾的下划线
        .ifEmpty { "file" } // 如果清理后为空，使用默认名称
}

// 关键审批动作数据结构
data class KeyAction(
    val actionType: ActionType,
    val user: User,
    val description: String
)

enum class ActionType {
    SUBMIT,    // 提交申请
    APPROVE,   // 审批通过
    REJECT,    // 审批拒绝
    TRANSFER,  // 流转
    COMMENT,   // 留言
    PENDING    // 待处理
}

// 完整历史记录数据结构
data class HistoryItem(
    val actionType: ActionType,
    val user: User,
    val time: String,
    val description: String,
    val comment: String? = null
)

// 构建关键审批动作（简化信息，不显示时间）
private fun buildKeyActions(approval: ApprovalRequest): List<KeyAction> {
    val actions = mutableListOf<KeyAction>()

    // 1. 申请提交
    actions.add(KeyAction(
        actionType = ActionType.SUBMIT,
        user = approval.applicant,
        description = "${approval.applicant.realName} 提交申请"
    ))

    // 2. 从审批步骤中提取关键动作
    approval.approvalSteps.forEach { step ->
        when (step.status) {
            StepStatus.COMPLETED -> {
                actions.add(KeyAction(
                    actionType = ActionType.APPROVE,
                    user = step.approver,
                    description = "${step.approver.realName} 审批通过"
                ))
            }
            StepStatus.REJECTED -> {
                actions.add(KeyAction(
                    actionType = ActionType.REJECT,
                    user = step.approver,
                    description = "${step.approver.realName} 审批拒绝"
                ))
            }
            StepStatus.CURRENT -> {
                actions.add(KeyAction(
                    actionType = ActionType.PENDING,
                    user = step.approver,
                    description = "${step.approver.realName} 正在审批"
                ))
            }
            StepStatus.PENDING -> {
                actions.add(KeyAction(
                    actionType = ActionType.PENDING,
                    user = step.approver,
                    description = "${step.approver.realName} 待审批"
                ))
            }
            StepStatus.TRANSFERRED -> {
                // 已流转的步骤不显示在关键动作中，因为流转记录会单独处理
            }
        }
    }

    // 3. 从评论中提取留言动作（排除审批相关的评论，避免重复）
    approval.comments.filter {
        it.commentType != CommentType.APPROVAL && it.commentType != CommentType.REJECTION
    }.forEach { comment ->
        actions.add(KeyAction(
            actionType = ActionType.COMMENT,
            user = comment.commenter,
            description = "${comment.commenter.realName} 添加意见"
        ))
    }

    return actions
}

// 构建完整操作历史（包含时间和详细信息）
private fun buildCompleteHistory(approval: ApprovalRequest): List<HistoryItem> {
    val history = mutableListOf<HistoryItem>()

    // 1. 申请提交
    history.add(HistoryItem(
        actionType = ActionType.SUBMIT,
        user = approval.applicant,
        time = approval.submitTime,
        description = "提交申请",
        comment = approval.description
    ))

    // 2. 审批步骤历史
    approval.approvalSteps.forEach { step ->
        when (step.status) {
            StepStatus.COMPLETED -> {
                history.add(HistoryItem(
                    actionType = ActionType.APPROVE,
                    user = step.approver,
                    time = step.processTime ?: "",
                    description = "审批通过",
                    comment = step.comment
                ))
            }
            StepStatus.REJECTED -> {
                history.add(HistoryItem(
                    actionType = ActionType.REJECT,
                    user = step.approver,
                    time = step.processTime ?: "",
                    description = "审批拒绝",
                    comment = step.comment
                ))
            }
            StepStatus.CURRENT -> {
                history.add(HistoryItem(
                    actionType = ActionType.PENDING,
                    user = step.approver,
                    time = "",
                    description = "正在审批中"
                ))
            }
            StepStatus.PENDING -> {
                history.add(HistoryItem(
                    actionType = ActionType.PENDING,
                    user = step.approver,
                    time = "",
                    description = "待审批"
                ))
            }
            StepStatus.TRANSFERRED -> {
                // 已流转的步骤不显示在历史中，因为流转记录会单独处理
            }
        }
    }

    // 3. 评论历史（排除审批相关的评论，避免重复）
    approval.comments.filter {
        it.commentType != CommentType.APPROVAL && it.commentType != CommentType.REJECTION
    }.forEach { comment ->
        history.add(HistoryItem(
            actionType = ActionType.COMMENT,
            user = comment.commenter,
            time = comment.commentTime,
            description = "添加意见",
            comment = comment.content
        ))
    }

    // 4. 流转历史（如果有）
    // TODO: 从transferHistory中提取流转记录

    // 按时间排序
    return history.sortedBy { it.time }
}

// 审批状态概览组件（主要区域 - 显示状态）
@Composable
private fun ApprovalStatusOverview(approval: ApprovalRequest) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 当前状态
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val (statusIcon, statusColor, statusText) = when (approval.status) {
                ApprovalStatus.PENDING -> Triple(
                    Icons.Default.Schedule,
                    Color(0xFFF59E0B),
                    "审批进行中"
                )
                ApprovalStatus.APPROVED -> Triple(
                    Icons.Default.CheckCircle,
                    Color(0xFF10B981),
                    "审批已通过"
                )
                ApprovalStatus.REJECTED -> Triple(
                    Icons.Default.Cancel,
                    Color(0xFFEF4444),
                    "审批已拒绝"
                )
            }

            Icon(
                imageVector = statusIcon,
                contentDescription = null,
                tint = statusColor,
                modifier = Modifier.size(18.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = statusText,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = statusColor
            )
        }

        // 当前审批人信息
        val currentApprovers = getCurrentApprovers(approval.approvalSteps)
        if (currentApprovers.isNotEmpty()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = null,
                    tint = Color(0xFF64748B),
                    modifier = Modifier.size(16.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "当前审批人：${currentApprovers.joinToString("、") { it.approver.realName }}",
                    fontSize = 14.sp,
                    color = Color(0xFF374151)
                )
            }
        }
    }
}

// 审批节点详情组件（展开区域 - 显示完整的操作历史）
@Composable
private fun ApprovalNodesDetail(approval: ApprovalRequest) {
    // 构建完整的操作历史时间线
    val operationHistory = buildOperationHistory(approval)

    LazyColumn(
        modifier = Modifier.heightIn(max = 400.dp),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        items(operationHistory.size) { index ->
            val operation = operationHistory[index]
            TimelineItem(
                operation = operation,
                isFirst = index == 0,
                isLast = index == operationHistory.size - 1
            )
        }
    }
}

// 操作历史数据类
private data class OperationHistoryItem(
    val icon: ImageVector,
    val iconColor: Color,
    val title: String,
    val user: User,
    val time: String,
    val description: String,
    val comment: String?,
    val timestamp: Long // 用于排序
)

// 构建完整的操作历史
private fun buildOperationHistory(approval: ApprovalRequest): List<OperationHistoryItem> {
    val operations = mutableListOf<OperationHistoryItem>()

    Log.d("ApprovalDetail", "=== 构建操作历史 ===")
    Log.d("ApprovalDetail", "审批步骤数: ${approval.approvalSteps.size}")
    Log.d("ApprovalDetail", "流转记录数: ${approval.transferRecords.size}")
    Log.d("ApprovalDetail", "评论数: ${approval.comments.size}")

    // 1. 申请提交
    // 获取初始审批人名单（排除申请人自己，排除流转后新增的审批人）
    // 通过流转记录来识别哪些是原始审批人
    val transferredToUsers = approval.transferRecords.mapNotNull { it.toUser?.id }.toSet()
    val originalApprovers = approval.approvalSteps
        .filter { step ->
            step.approver.id != approval.applicant.id && // 排除申请人
            step.approver.id !in transferredToUsers // 排除流转接收人
        }
        .map { it.approver.realName }
        .distinct()

    val submitDescription = if (originalApprovers.isNotEmpty()) {
        "发起申请给 ${originalApprovers.joinToString("、")}"
    } else {
        "发起申请"
    }

    operations.add(
        OperationHistoryItem(
            icon = Icons.Default.Send,
            iconColor = Color(0xFF6366F1),
            title = submitDescription,
            user = approval.applicant,
            time = approval.submitTime,
            description = "",
            comment = null,
            timestamp = parseTimeToTimestamp(approval.submitTime)
        )
    )

    // 2. 审批步骤操作（排除申请人自己的步骤）
    approval.approvalSteps.forEach { step ->
        // 过滤掉申请人的审批步骤，申请人不应该审批自己的申请
        if (step.approver.id != approval.applicant.id) {
            if (step.status == StepStatus.COMPLETED) {
                operations.add(
                    OperationHistoryItem(
                        icon = Icons.Default.CheckCircle,
                        iconColor = Color(0xFF059669),
                        title = "审批通过",
                        user = step.approver,
                        time = step.processTime ?: "",
                        description = "",
                        comment = null, // 不显示审批步骤的评论，避免重复
                        timestamp = parseTimeToTimestamp(step.processTime ?: "")
                    )
                )
            } else if (step.status == StepStatus.REJECTED) {
                operations.add(
                    OperationHistoryItem(
                        icon = Icons.Default.Cancel,
                        iconColor = Color(0xFFDC2626),
                        title = "审批拒绝",
                        user = step.approver,
                        time = step.processTime ?: "",
                        description = "",
                        comment = null, // 不显示审批步骤的评论，避免重复
                        timestamp = parseTimeToTimestamp(step.processTime ?: "")
                    )
                )
            }
        }
    }

    // 3. 流转记录
    approval.transferRecords.forEach { transfer ->
        if (transfer.fromUser != null && transfer.toUser != null) {
            operations.add(
                OperationHistoryItem(
                    icon = Icons.Default.SwapHoriz,
                    iconColor = Color(0xFFF59E0B),
                    title = "流转给 ${transfer.toUser.realName}",
                    user = transfer.fromUser,
                    time = transfer.transferTime ?: "",
                    description = "",
                    comment = transfer.comment.takeIf { it.isNotBlank() },
                    timestamp = parseTimeToTimestamp(transfer.transferTime ?: "")
                )
            )
        }
    }

    // 4. 评论记录（只显示独立的评论，排除审批和流转相关的评论）
    Log.d("ApprovalDetail", "所有评论数据:")
    approval.comments.forEachIndexed { index, comment ->
        Log.d("ApprovalDetail", "评论[$index]: 用户=${comment.commenter.realName}, 类型=${comment.commentType}, 内容=${comment.content}")
    }

    approval.comments.filter {
        !it.commentTime.isNullOrBlank() &&
        it.commentType != CommentType.APPROVAL &&
        it.commentType != CommentType.REJECTION &&
        it.commentType != CommentType.TRANSFER // 排除流转评论，避免与流转记录重复
    }.forEach { comment ->
        Log.d("ApprovalDetail", "显示评论: 用户=${comment.commenter.realName}, 类型=${comment.commentType}, 内容=${comment.content}")
        operations.add(
            OperationHistoryItem(
                icon = Icons.AutoMirrored.Filled.Comment,
                iconColor = Color(0xFF7C3AED),
                title = "添加评论",
                user = comment.commenter,
                time = comment.commentTime ?: "",
                description = "",
                comment = comment.content.takeIf { it.isNotBlank() },
                timestamp = parseTimeToTimestamp(comment.commentTime ?: "")
            )
        )
    }

    // 添加排序前的调试日志
    Log.d("ApprovalDetail", "排序前的操作历史:")
    operations.forEachIndexed { index, op ->
        Log.d("ApprovalDetail", "[$index] ${op.title} - ${op.user.realName} - ${op.time} - timestamp: ${op.timestamp}")
    }

    // 按时间正序排列（最早的在前面，符合操作发生的先后顺序）
    val sortedOperations = operations.sortedBy { it.timestamp }

    Log.d("ApprovalDetail", "排序后的操作历史:")
    sortedOperations.forEachIndexed { index, op ->
        Log.d("ApprovalDetail", "[$index] ${op.title} - ${op.user.realName} - ${op.time} - timestamp: ${op.timestamp}")
    }

    return sortedOperations
}

// 时间解析函数
private fun parseTimeToTimestamp(timeString: String): Long {
    if (timeString.isBlank()) {
        Log.d("ApprovalDetail", "时间字符串为空")
        return 0L
    }

    Log.d("ApprovalDetail", "解析时间字符串: '$timeString'")

    // 支持的时间格式列表
    val formatters = listOf(
        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"),
        java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
        java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"),
        java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm:ss"),
        java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm")
    )

    for (formatter in formatters) {
        try {
            val localDateTime = java.time.LocalDateTime.parse(timeString, formatter)
            val timestamp = localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli()
            Log.d("ApprovalDetail", "成功解析时间: '$timeString' -> $timestamp")
            return timestamp
        } catch (e: Exception) {
            // 继续尝试下一个格式
        }
    }

    // 如果所有格式都失败，尝试解析为纯数字时间戳
    try {
        val timestamp = timeString.toLong()
        Log.d("ApprovalDetail", "解析为时间戳: '$timeString' -> $timestamp")
        return timestamp
    } catch (e: Exception) {
        // 最后的尝试：使用当前时间
        Log.w("ApprovalDetail", "无法解析时间: '$timeString'，使用当前时间")
        return System.currentTimeMillis()
    }
}

// 时间线项组件
@Composable
private fun TimelineItem(
    operation: OperationHistoryItem,
    isFirst: Boolean,
    isLast: Boolean
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        // 时间线左侧
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.width(24.dp)
        ) {
            // 上方连接线
            if (!isFirst) {
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(12.dp)
                        .background(Color(0xFFE5E7EB))
                )
            }

            // 节点圆点
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .clip(CircleShape)
                    .background(operation.iconColor),
                contentAlignment = Alignment.Center
            ) {}

            // 下方连接线
            if (!isLast) {
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(32.dp)
                        .background(Color(0xFFE5E7EB))
                )
            }
        }

        // 内容区域
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 12.dp, bottom = if (isLast) 0.dp else 16.dp)
        ) {
            // 主要信息
            Text(
                text = "${operation.user.realName} ${operation.title}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF1F2937)
            )

            // 时间信息
            if (operation.time.isNotBlank()) {
                Text(
                    text = formatDateTime(operation.time),
                    fontSize = 12.sp,
                    color = Color(0xFF9CA3AF),
                    modifier = Modifier.padding(top = 2.dp)
                )
            }

            // 备注或评论
            if (!operation.comment.isNullOrBlank()) {
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    color = Color(0xFFF9FAFB),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = operation.comment,
                        fontSize = 13.sp,
                        color = Color(0xFF4B5563),
                        modifier = Modifier.padding(12.dp)
                    )
                }
            }
        }
    }
}

// 详细历史项组件（显示完整信息和时间）
@Composable
private fun DetailedHistoryItem(historyItem: HistoryItem) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8FAFC)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 动作类型图标
                val (icon, iconColor) = when (historyItem.actionType) {
                    ActionType.SUBMIT -> Icons.Default.Send to Color(0xFF3B82F6)
                    ActionType.APPROVE -> Icons.Default.CheckCircle to Color(0xFF10B981)
                    ActionType.REJECT -> Icons.Default.Cancel to Color(0xFFEF4444)
                    ActionType.TRANSFER -> Icons.Default.SwapHoriz to Color(0xFFF59E0B)
                    ActionType.COMMENT -> Icons.AutoMirrored.Filled.Comment to Color(0xFF8B5CF6)
                    ActionType.PENDING -> Icons.Default.Schedule to Color(0xFF64748B)
                }

                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(18.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "${historyItem.user.realName} ${historyItem.description}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFF374151)
                    )

                    if (historyItem.time.isNotBlank()) {
                        Text(
                            text = formatDateTime(historyItem.time),
                            fontSize = 12.sp,
                            color = Color(0xFF64748B)
                        )
                    }
                }
            }

            // 显示备注或评论内容
            if (!historyItem.comment.isNullOrBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFEFF6FF),
                    shape = RoundedCornerShape(6.dp)
                ) {
                    Text(
                        text = historyItem.comment,
                        fontSize = 13.sp,
                        color = Color(0xFF374151),
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        }
    }
}